<?php
// Incluir archivos necesarios
require_once './include/language.php';
?>
<!DOCTYPE html>
<html lang="<?php echo $current_language; ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cómo Usar - FastlyIA</title>

    <!-- ⚡ Script INLINE para evitar flash del modo oscuro -->
    <script>
        (function() {
            if (localStorage.getItem('darkMode') === 'enabled' ||
                (localStorage.getItem('darkMode') === null && window.matchMedia('(prefers-color-scheme: dark)').matches)) {
                document.documentElement.classList.add('dark');
            }
        })();
    </script>

    <!-- Script para aplicar el modo oscuro inmediatamente -->
    <script type="module" src="./js/colorMode.js"></script>
    <link rel="stylesheet" href="./css/styles.css">
    <link rel="stylesheet" href="./css/custom.css">

    <?php  require './include/analytics.php'; ?>
    <script async src="https://tally.so/widgets/embed.js"></script>

    <!-- Scripts de Supabase para autenticación -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js"></script>
    <script src="./js/auth.js"></script>
    <script src="./js/language.js"></script>
</head>
<body class="bg-gray-50 dark:bg-gray-900">
    <!-- Navegación -->
    <nav class="bg-white shadow-sm sticky top-0 z-10 dark:bg-gray-800">

    <?php  require './include/nav.php';  ?>

        <!-- Menú móvil desplegable -->
        <?php require './include/nav_movil.php';?>
    </nav>

    <div class="container mx-auto px-4 py-8">
        <div class="max-w-3xl mx-auto content">
            <h1 class="text-4xl font-bold text-center mb-4 dark:text-white"><?php echo t('how_to_use_title'); ?></h1>
            <p class="text-gray-600 text-center mb-12 max-w-2xl mx-auto dark:text-gray-300">
                <?php echo t('how_to_use_subtitle'); ?>
            </p>

            <div class="grid gap-8 mb-12">
                <!-- Paso 1 -->
                <div class="bg-white rounded-xl shadow-md p-6 step-card dark:bg-gray-800">
                    <div class="flex items-start">
                        <div class="flex-shrink-0 bg-blue-100 text-blue-800 rounded-full h-10 w-10 flex items-center justify-center font-bold text-xl mr-4 dark:bg-blue-900 dark:text-blue-200">1</div>
                        <div>
                            <h2 class="text-2xl font-bold mb-3 dark:text-white"><?php echo t('step_1_title'); ?></h2>
                            <p class="text-gray-600 mb-4 dark:text-gray-300">
                                <?php echo t('step_1_text'); ?>
                            </p>
                            <p class="text-gray-600 dark:text-gray-300">
                                <span class="font-semibold"><?php echo t('step_1_coding'); ?></span><br>
                                <span class="font-semibold"><?php echo t('step_1_writing'); ?></span>
                            </p>
                        </div>
                    </div>
                </div>

                <!-- Paso 2 -->
                <div class="bg-white rounded-xl shadow-md p-6 step-card dark:bg-gray-800">
                    <div class="flex items-start">
                        <div class="flex-shrink-0 bg-blue-100 text-blue-800 rounded-full h-10 w-10 flex items-center justify-center font-bold text-xl mr-4 dark:bg-blue-900 dark:text-blue-200">2</div>
                        <div>
                            <h2 class="text-2xl font-bold mb-3 dark:text-white"><?php echo t('step_2_title'); ?></h2>
                            <p class="text-gray-600 mb-4 dark:text-gray-300">
                                <?php echo t('step_2_text'); ?>
                            </p>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                                <div class="bg-gray-50 p-3 rounded-lg dark:bg-gray-700">
                                    <h3 class="font-semibold mb-1 dark:text-white"><?php echo t('step_2_coding_tools'); ?></h3>
                                    <ul class="text-gray-600 list-disc list-inside dark:text-gray-300">
                                        <li>v0.dev - Interfaces con React</li>
                                        <li>Lovable.dev - Apps completas</li>
                                        <li>Cursor - IDE con IA</li>
                                        <li>Bolt.new - Creación rápida</li>
                                    </ul>
                                </div>
                                <div class="bg-gray-50 p-3 rounded-lg dark:bg-gray-700">
                                    <h3 class="font-semibold mb-1 dark:text-white"><?php echo t('step_2_writing_tools'); ?></h3>
                                    <ul class="text-gray-600 list-disc list-inside dark:text-gray-300">
                                        <li>Jasper - Asistente de escritura</li>
                                        <li>Copy.ai - Copywriting</li>
                                        <li>Writesonic - Contenido SEO</li>
                                        <li>Claude/ChatGPT - Uso general</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Paso 3 -->
                <div class="bg-white rounded-xl shadow-md p-6 step-card dark:bg-gray-800">
                    <div class="flex items-start">
                        <div class="flex-shrink-0 bg-blue-100 text-blue-800 rounded-full h-10 w-10 flex items-center justify-center font-bold text-xl mr-4 dark:bg-blue-900 dark:text-blue-200">3</div>
                        <div>
                            <h2 class="text-2xl font-bold mb-3 dark:text-white"><?php echo t('step_3_title'); ?></h2>
                            <p class="text-gray-600 mb-4 dark:text-gray-300">
                                <?php echo t('step_3_text'); ?>
                            </p>
                            <div class="bg-gray-50 p-4 rounded-lg dark:bg-gray-700">
                                <h3 class="font-semibold mb-2 dark:text-white"><?php echo t('step_3_tips_title'); ?></h3>
                                <ul class="text-gray-600 list-disc list-inside space-y-2 dark:text-gray-300">
                                    <li><?php echo t('step_3_tip_1'); ?></li>
                                    <li><?php echo t('step_3_tip_2'); ?></li>
                                    <li><?php echo t('step_3_tip_3'); ?></li>
                                    <li><?php echo t('step_3_tip_4'); ?></li>
                                    <li><?php echo t('step_3_tip_5'); ?></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Paso 4 -->
                <div class="bg-white rounded-xl shadow-md p-6 step-card dark:bg-gray-800">
                    <div class="flex items-start">
                        <div class="flex-shrink-0 bg-blue-100 text-blue-800 rounded-full h-10 w-10 flex items-center justify-center font-bold text-xl mr-4 dark:bg-blue-900 dark:text-blue-200">4</div>
                        <div>
                            <h2 class="text-2xl font-bold mb-3 dark:text-white"><?php echo t('step_4_title'); ?></h2>
                            <p class="text-gray-600 mb-4 dark:text-gray-300">
                                <?php echo t('step_4_text'); ?>
                            </p>
                            <div class="bg-blue-50 border-l-4 border-blue-500 p-4 rounded-r-lg dark:bg-blue-900/20 dark:border-blue-400">
                                <p class="text-gray-700 italic dark:text-gray-300">
                                    "<?php echo t('step_4_quote'); ?>"
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-xl shadow-md p-8 mb-10 dark:bg-gray-800">
                <h2 class="text-2xl font-bold mb-4 dark:text-white"><?php echo t('additional_tips_title'); ?></h2>

                <div class="space-y-6">
                    <div>
                        <h3 class="text-xl font-semibold mb-2 dark:text-white"><?php echo t('tip_specific_title'); ?></h3>
                        <p class="text-gray-600 dark:text-gray-300">
                            <?php echo t('tip_specific_text'); ?>
                        </p>
                    </div>

                    <div>
                        <h3 class="text-xl font-semibold mb-2 dark:text-white"><?php echo t('tip_iterate_title'); ?></h3>
                        <p class="text-gray-600 dark:text-gray-300">
                            <?php echo t('tip_iterate_text'); ?>
                        </p>
                    </div>

                    <div>
                        <h3 class="text-xl font-semibold mb-2 dark:text-white"><?php echo t('tip_customize_title'); ?></h3>
                        <p class="text-gray-600 dark:text-gray-300">
                            <?php echo t('tip_customize_text'); ?>
                        </p>
                    </div>
                </div>
            </div>

            <div class="bg-blue-50 rounded-xl shadow-sm p-6 text-center dark:bg-blue-900/20">
                <h2 class="text-2xl font-bold mb-3 dark:text-white"><?php echo t('ready_to_start_title'); ?></h2>
                <p class="text-gray-700 mb-6 max-w-xl mx-auto dark:text-gray-300">
                    <?php echo t('ready_to_start_text'); ?>
                </p>
                <a href="/" class="inline-block bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors text-base font-semibold dark:bg-blue-500 dark:hover:bg-blue-600">
                    <?php echo t('create_first_prompt'); ?>
                </a>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <?php
require './include/footer.php';
?>



    <script>
        // Pasar las traducciones del modo oscuro al JavaScript
        window.translations = {
            dark_mode_text: <?php echo json_encode(t('dark_mode_text')); ?>,
            light_mode_text: <?php echo json_encode(t('light_mode_text')); ?>
        };

        // Funciones globales para la autenticación (compatibilidad con nav.php)
        function showLoginModal() {
            if (window.authSystem) {
                window.authSystem.showLoginModal();
            }
        }

        function showRegisterModal() {
            if (window.authSystem) {
                window.authSystem.showRegisterModal();
            }
        }

        function logout() {
            if (window.authSystem) {
                window.authSystem.logout();
            }
        }
    </script>
    <script src="./js/movilMenu.js"></script>
</body>
</html>