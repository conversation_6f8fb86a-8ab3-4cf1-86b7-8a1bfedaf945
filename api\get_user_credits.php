<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

require_once '../include/user_manager.php';

try {
    // Verificar método
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Método no permitido');
    }
    
    // Obtener datos del request
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!isset($input['user_id'])) {
        throw new Exception('ID de usuario requerido');
    }
    
    $userId = $input['user_id'];
    
    // Crear instancia del UserManager
    $userManager = new UserManager();
    
    // Obtener créditos del usuario
    $credits = $userManager->getUserCredits($userId);
    
    // Respuesta exitosa
    echo json_encode([
        'success' => true,
        'credits' => $credits
    ]);
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
