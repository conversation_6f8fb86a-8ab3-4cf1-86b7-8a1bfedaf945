/**
 * Funciones para actualizar formularios con traducciones dinámicas
 */

// Función para actualizar los textos de los formularios
function updateFormTranslations() {
    if (!window.translations) return;

    // Actualizar títulos de formularios
    const projectTitle = document.querySelector('#step-3-codigo h2');
    if (projectTitle) {
        projectTitle.textContent = window.translations.form_describe_project;
    }

    const contentTitle = document.querySelector('#step-3-texto h2');
    if (contentTitle) {
        contentTitle.textContent = window.translations.form_describe_content;
    }

    // Actualizar formulario de código
    const codeForm = document.getElementById('project-form-codigo');
    if (codeForm) {
        // Labels y placeholders del formulario de código
        const projectTypeLabel = codeForm.querySelector('label[for="projectType"], label:first-of-type');
        if (projectTypeLabel) projectTypeLabel.textContent = window.translations.form_project_type_label;

        const projectTypeInput = codeForm.querySelector('input[name="projectType"]');
        if (projectTypeInput) projectTypeInput.placeholder = window.translations.form_project_type_placeholder;

        const featuresLabel = codeForm.querySelectorAll('label')[1];
        if (featuresLabel) featuresLabel.textContent = window.translations.form_features_label;

        const featuresTextarea = codeForm.querySelector('textarea[name="features"]');
        if (featuresTextarea) featuresTextarea.placeholder = window.translations.form_features_placeholder;

        const styleLabel = codeForm.querySelectorAll('label')[2];
        if (styleLabel) styleLabel.textContent = window.translations.form_style_label;

        const styleInput = codeForm.querySelector('input[name="style"]');
        if (styleInput) styleInput.placeholder = window.translations.form_style_placeholder;

        const codeSubmitBtn = codeForm.querySelector('button[type="submit"]');
        if (codeSubmitBtn) codeSubmitBtn.textContent = window.translations.form_generate_prompt;
    }

    // Actualizar formulario de contenido
    const textForm = document.getElementById('project-form-texto');
    if (textForm) {
        // Labels y placeholders del formulario de texto
        const contentTypeLabel = textForm.querySelector('label[for="contentType"], label:first-of-type');
        if (contentTypeLabel) contentTypeLabel.textContent = window.translations.form_content_type_label;

        const contentTypeInput = textForm.querySelector('input[name="contentType"]');
        if (contentTypeInput) contentTypeInput.placeholder = window.translations.form_content_type_placeholder;

        const mainTopicLabel = textForm.querySelectorAll('label')[1];
        if (mainTopicLabel) mainTopicLabel.textContent = window.translations.form_main_topic_label;

        const mainTopicTextarea = textForm.querySelector('textarea[name="mainTopic"]');
        if (mainTopicTextarea) mainTopicTextarea.placeholder = window.translations.form_main_topic_placeholder;

        const toneLabel = textForm.querySelectorAll('label')[2];
        if (toneLabel) toneLabel.textContent = window.translations.form_tone_label;

        const toneInput = textForm.querySelector('input[name="tone"]');
        if (toneInput) toneInput.placeholder = window.translations.form_tone_placeholder;

        const textSubmitBtn = textForm.querySelector('button[type="submit"]');
        if (textSubmitBtn) textSubmitBtn.textContent = window.translations.form_generate_prompt;
    }

    // Actualizar botones de "Volver"
    const backButtons = document.querySelectorAll('.back-button span');
    backButtons.forEach(span => {
        if (span && window.translations.form_back) {
            span.textContent = window.translations.form_back;
        }
    });
}

// Inicializar cuando el DOM esté listo
document.addEventListener('DOMContentLoaded', function() {
    // Actualizar traducciones al cargar
    updateFormTranslations();
    
    // Escuchar cambios de idioma
    document.addEventListener('languageChanged', function() {
        updateFormTranslations();
    });
});
