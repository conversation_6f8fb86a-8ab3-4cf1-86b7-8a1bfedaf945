document.addEventListener('DOMContentLoaded', () => {
    // Seleccionar todos los botones de copiar en la página
    const copyButtons = document.querySelectorAll('.copy-button');
    
    // Añadir evento a cada botón
    copyButtons.forEach(button => {
        button.addEventListener('click', async () => {
            // Obtener el contenido del prompt asociado a este botón
            const promptContainer = button.closest('.example-card').querySelector('.prompt-content');
            const promptText = promptContainer.textContent;
            
            try {
                await navigator.clipboard.writeText(promptText);
                // Cambiar el texto del botón temporalmente
                const originalText = button.textContent;
                button.textContent = '¡Copiado!';
                
                // Restaurar el texto original después de 2 segundos
                setTimeout(() => {
                    button.textContent = originalText;
                }, 2000);
            } catch (err) {
                console.error('Error al copiar:', err);
            }
        });
    });
});
