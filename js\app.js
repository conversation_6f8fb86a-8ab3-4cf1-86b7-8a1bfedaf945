// Funciones globales para la autenticación (compatibilidad con nav.php)
function showLoginModal() {
    if (window.authSystem) {
        window.authSystem.showLoginModal();
    }
}

function showRegisterModal() {
    if (window.authSystem) {
        window.authSystem.showRegisterModal();
    }
}

function logout() {
    if (window.authSystem) {
        window.authSystem.logout();
    }
}

document.addEventListener('DOMContentLoaded', () => {
    // El selector de idioma ahora se maneja en language.js
    const slides = document.querySelectorAll('.slide');
    const slidesContainer = document.querySelector('.slides');
    const progressBar = document.getElementById('progress-bar');
    const projectFormCodigo = document.getElementById('project-form-codigo');
    const projectFormTexto = document.getElementById('project-form-texto');
    // El botón flotante ya no existe
    const backToFormButton = document.getElementById('back-to-form');
    const loadingOverlay = document.getElementById('loading-overlay');
    const policyCheckbox = document.getElementById('policy-checkbox');
    const copyButton = document.getElementById('copy-prompt');

    let currentStep = 1;
    const totalSteps = 4;
    let selectedType = null;
    let selectedTool = null;

    // El efecto typewriter ahora se maneja en typewriter.js

    // Validar checkbox de políticas
    function updateOptionCards() {
        const optionCards = document.querySelectorAll('.option-card');

        if (policyCheckbox && optionCards.length > 0) {
            if (policyCheckbox.checked) {
                optionCards.forEach(card => {
                    card.classList.remove('opacity-50', 'pointer-events-none');
                    card.removeAttribute('disabled');
                });
            } else {
                optionCards.forEach(card => {
                    card.classList.add('opacity-50', 'pointer-events-none');
                    card.setAttribute('disabled', 'true');
                });
            }
        }
    }

    // Escuchar cambios en el checkbox
    if (policyCheckbox) {
        policyCheckbox.addEventListener('change', updateOptionCards);
        // Inicializar estado
        updateOptionCards();
    }


    // Actualizar la barra de progreso
    function updateProgress() {
        const progress = (currentStep / totalSteps) * 100;
        progressBar.style.width = `${progress}%`;
    }

    // Esta función ya no es necesaria ya que eliminamos el botón flotante
    function updateBackButton() {
        // No hacemos nada, ya que los botones de retroceso están en cada slide
        // y se muestran/ocultan automáticamente con el slide
    }

    // Ajustar para dispositivos móviles - BREAKPOINT UNIFICADO
    function handleResize() {
        const slides = document.querySelectorAll('.slide');
        const slidesContainer = document.querySelector('.slides');

        if (!slides.length || !slidesContainer) return;

        const isMobile = window.innerWidth < 768; // CAMBIADO A 768px
        const slideWidth = slides[0].offsetWidth;

        // Si cambiamos de tamaño, ajustar la posición actual
        slidesContainer.style.transform = `translateX(-${(currentStep - 1) * slideWidth}px)`;

        // En móvil, hacer ajustes adicionales
        if (isMobile) {
            // Ajustar tamaño de los contenedores
            document.querySelectorAll('.input-large').forEach(input => {
                if (input) {
                    input.style.fontSize = '1rem';
                    input.style.padding = '0.75rem';
                }
            });

            // Ajustar espaciado
            document.querySelectorAll('.space-y-4').forEach(container => {
                if (container) {
                    container.classList.replace('space-y-4', 'space-y-3');
                }
            });
        }
    }

    // Escuchar cambios de tamaño solo si estamos en una página con slides
    if (document.querySelector('.slides')) {
        window.addEventListener('resize', handleResize);
        // Inicializar
        handleResize();
    }

    // Manejar la navegación entre slides
    function goToSlide(step) {
        console.log("Navegando al paso:", step);

        // Calcular la posición de desplazamiento
        const slideWidth = slides[0].offsetWidth;
        slidesContainer.style.transition = 'transform 0.5s ease';
        slidesContainer.style.transform = `translateX(-${(step - 1) * slideWidth}px)`;



        // Quitar la clase active de todos los slides
        slides.forEach(slide => {
            slide.classList.remove('active');
        });

        // Preparar los slides correctos según el paso
        if (step === 3) {
            // Para el paso 3, preparar el formulario correcto según el tipo seleccionado
            const codigoForm = document.querySelector('.slide[data-step="3"][data-form="codigo"]');
            const textoForm = document.querySelector('.slide[data-step="3"][data-form="texto"]');

            // Ocultar ambos primero
            if (codigoForm) codigoForm.style.display = 'none';
            if (textoForm) textoForm.style.display = 'none';

            // Mostrar el correcto
            if (selectedType === 'codigo' && codigoForm) {
                codigoForm.classList.add('active');
                codigoForm.style.display = 'flex';
                setTimeout(() => {
                    // Asegurarse de que el slide esté visible después de la transición
                    codigoForm.style.display = 'flex';
                }, 500);
            } else if (selectedType === 'texto' && textoForm) {
                textoForm.classList.add('active');
                textoForm.style.display = 'flex';
                setTimeout(() => {
                    // Asegurarse de que el slide esté visible después de la transición
                    textoForm.style.display = 'flex';
                }, 500);
            }
        } else if (step === 2) {
            // Para el paso 2, preparar la herramienta correspondiente según el tipo seleccionado
            const codigoTools = document.querySelector('.slide[data-step="2"][data-type="codigo"]');
            const textoTools = document.querySelector('.slide[data-step="2"][data-type="texto"]');

            // Ocultar ambos primero
            if (codigoTools) codigoTools.style.display = 'none';
            if (textoTools) textoTools.style.display = 'none';

            // Mostrar el correcto según el tipo seleccionado
            if (selectedType === 'codigo' && codigoTools) {
                codigoTools.classList.add('active');
                codigoTools.style.display = 'flex';
                setTimeout(() => {
                    // Asegurarse de que el slide esté visible después de la transición
                    codigoTools.style.display = 'flex';
                }, 500);
            } else if (selectedType === 'texto' && textoTools) {
                textoTools.classList.add('active');
                textoTools.style.display = 'flex';
                setTimeout(() => {
                    // Asegurarse de que el slide esté visible después de la transición
                    textoTools.style.display = 'flex';
                }, 500);
            }
        } else {
            // Para otros pasos, activar el slide correspondiente
            const currentSlide = document.querySelector(`.slide[data-step="${step}"]`);
            if (currentSlide) {
                currentSlide.classList.add('active');
                currentSlide.style.display = 'flex';
            }
        }

        // Enfocar el campo de entrada si existe
        const activeSlide = document.querySelector('.slide.active');
        if (activeSlide) {
            const autofocusInput = activeSlide.querySelector('[data-autofocus]');
            if (autofocusInput) {
                setTimeout(() => autofocusInput.focus(), 600);
            }
        }

        currentStep = step;
        updateProgress();
        updateBackButton();
    }

    // Manejar la selección de opciones
    function handleOptionSelection(event) {
        const card = event.target.closest('.option-card');
        if (!card || card.hasAttribute('disabled')) return;

        const currentSlide = card.closest('.slide');
        const step = parseInt(currentSlide.dataset.step);

        // Remover selección previa
        currentSlide.querySelectorAll('.option-card').forEach(c => c.classList.remove('selected'));
        card.classList.add('selected');

        // Guardar la selección y avanzar automáticamente
        if (step === 1) {
            selectedType = card.dataset.value;
            // Avanzar automáticamente después de una breve pausa
            setTimeout(() => goToSlide(2), 300);
        } else if (step === 2) {
            selectedTool = card.dataset.value;
            // Avanzar automáticamente después de una breve pausa
            setTimeout(() => goToSlide(3), 300);
        }
    }


    // Función para generar prompts con la API
    async function generatePromptWithAPI(formData) {
        try {
            loadingOverlay.classList.add('active');

            // Crear el mensaje para enviar a la API
            let message = '';
            if (selectedType === 'codigo') {
                message = `Herramienta: ${selectedTool}\n\n`;
                message += `Crear un ${formData.projectType} con las siguientes características:\n`;
                formData.features.split(',').forEach(feature => {
                    message += `- ${feature.trim()}\n`;
                });
                message += `\nEstilo visual: ${formData.style}`;
            } else {
                message = `Herramienta: ${selectedTool}\n\n`;
                message += `Generar contenido para ${formData.contentType}\n`;
                message += `Características principales:\n`;
                formData.mainTopic.split(',').forEach(topic => {
                    message += `- ${topic.trim()}\n`;
                });
                message += `\nTono de voz: ${formData.tone}`;
            }

            const response = await fetch('./api/generate_prompt.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    message: message
                })
            });

            const data = await response.json();
            console.log('Respuesta de la API:', data);

            if (!response.ok) {
                // Verificar si es un error de límite excedido
                if (response.status === 429) {
                    // Mostrar el modal de límite excedido
                    const rateLimitModal = document.getElementById('rate-limit-modal');
                    if (rateLimitModal) {
                        rateLimitModal.classList.remove('hidden');
                    }
                    throw new Error('Límite de solicitudes excedido');
                } else {
                    // Mostrar el modal de error genérico
                    const errorModal = document.getElementById('error-modal');
                    const errorMessage = document.getElementById('error-message');
                    if (errorModal && errorMessage) {
                        errorMessage.textContent = `Error: ${response.statusText || 'Error en la solicitud'}`;
                        errorModal.classList.remove('hidden');
                    }
                    throw new Error(`Error en la solicitud: ${response.statusText}`);
                }
            }

            if (data.prompt) {
                console.log('Prompt generado:', data.prompt);
                return data.prompt;
            } else if (data.error) {
                // Verificar si el error es por límite excedido
                if (data.status === 429) {
                    // Mostrar el modal de límite excedido
                    const rateLimitModal = document.getElementById('rate-limit-modal');
                    if (rateLimitModal) {
                        rateLimitModal.classList.remove('hidden');
                    }
                    throw new Error('Límite de solicitudes excedido');
                } else {
                    // Mostrar el modal de error genérico
                    const errorModal = document.getElementById('error-modal');
                    const errorMessage = document.getElementById('error-message');
                    if (errorModal && errorMessage) {
                        errorMessage.textContent = data.error || 'Error al generar el prompt';
                        errorModal.classList.remove('hidden');
                    }
                    throw new Error(data.error || 'Error al generar el prompt');
                }
            } else {
                // Mostrar el modal de error genérico
                const errorModal = document.getElementById('error-modal');
                const errorMessage = document.getElementById('error-message');
                if (errorModal && errorMessage) {
                    errorMessage.textContent = 'Error al generar el prompt: respuesta inesperada del servidor';
                    errorModal.classList.remove('hidden');
                }
                throw new Error('Error al generar el prompt: respuesta inesperada del servidor');
            }
        } catch (error) {
            console.error('Error:', error);

            // Si el error no es de límite excedido y no se ha mostrado un modal específico
            if (!error.message.includes('Límite de solicitudes excedido') && document.getElementById('error-modal').classList.contains('hidden')) {
                // Mostrar el modal de error genérico
                const errorModal = document.getElementById('error-modal');
                const errorMessage = document.getElementById('error-message');
                if (errorModal && errorMessage) {
                    errorMessage.textContent = error.message || 'Ha ocurrido un error al procesar tu solicitud';
                    errorModal.classList.remove('hidden');
                }
            }

            // Devolver un mensaje de error para mostrar en la interfaz
            return "No se pudo generar el prompt debido a un error. Por favor, inténtalo de nuevo más tarde.";
        } finally {
            loadingOverlay.classList.remove('active');
        }
    }

    // Event Listeners
    document.addEventListener('click', handleOptionSelection);

    // Manejar el formulario para codificación
    projectFormCodigo.addEventListener('submit', async (e) => {
        e.preventDefault();

        const formData = {
            projectType: projectFormCodigo.querySelector('[name="projectType"]').value,
            features: projectFormCodigo.querySelector('[name="features"]').value,
            style: projectFormCodigo.querySelector('[name="style"]').value
        };

        const finalPrompt = await generatePromptWithAPI(formData);
        document.getElementById('final-prompt').textContent = finalPrompt;

        // Guardar en historial si el usuario está autenticado
        if (window.historySystem && window.historySystem.isAuthenticated && finalPrompt) {
            const historyData = {
                tool_type: 'coding',
                tool_name: selectedTool || 'Herramienta de código',
                prompt_title: `${selectedTool} - ${formData.projectType}`,
                prompt_content: finalPrompt,
                form_data: formData
            };

            // Disparar evento para que el sistema de historial lo capture
            document.dispatchEvent(new CustomEvent('promptGenerated', {
                detail: historyData
            }));
        }

        goToSlide(4);
    });

    // Manejar el formulario para escritura
    projectFormTexto.addEventListener('submit', async (e) => {
        e.preventDefault();

        const formData = {
            contentType: projectFormTexto.querySelector('[name="contentType"]').value,
            mainTopic: projectFormTexto.querySelector('[name="mainTopic"]').value,
            tone: projectFormTexto.querySelector('[name="tone"]').value
        };

        const finalPrompt = await generatePromptWithAPI(formData);
        document.getElementById('final-prompt').textContent = finalPrompt;

        // Guardar en historial si el usuario está autenticado
        if (window.historySystem && window.historySystem.isAuthenticated && finalPrompt) {
            const historyData = {
                tool_type: 'writing',
                tool_name: selectedTool || 'Herramienta de escritura',
                prompt_title: `${selectedTool} - ${formData.contentType}`,
                prompt_content: finalPrompt,
                form_data: formData
            };

            // Disparar evento para que el sistema de historial lo capture
            document.dispatchEvent(new CustomEvent('promptGenerated', {
                detail: historyData
            }));
        }

        goToSlide(4);
    });

    // Botones de navegación
    // El botón flotante ya no existe, así que no necesitamos este listener
    // backButton.addEventListener('click', () => {
    //     goToSlide(currentStep - 1);
    // });

    // Botones de retroceso en cada slide
    const backButton2 = document.getElementById('back-button-2');
    const backButton3 = document.getElementById('back-button-3');
    const backButtonCodigo = document.getElementById('back-button-codigo');
    const backButtonTexto = document.getElementById('back-button-texto');
    const backButtonFinal = document.getElementById('back-button-final');

    // Botones del paso 2 (vuelven al paso 1)
    if (backButton2) {
        backButton2.addEventListener('click', () => {
            goToSlide(1);
        });
    }

    if (backButton3) {
        backButton3.addEventListener('click', () => {
            goToSlide(1);
        });
    }

    // Botones del paso 3 (vuelven al paso 2)
    if (backButtonCodigo) {
        backButtonCodigo.addEventListener('click', () => {
            goToSlide(2);
        });
    }

    if (backButtonTexto) {
        backButtonTexto.addEventListener('click', () => {
            goToSlide(2);
        });
    }

    // Botón del paso 4 (vuelve al paso 3)
    if (backButtonFinal) {
        backButtonFinal.addEventListener('click', () => {
            goToSlide(3);
        });
    }



    backToFormButton.addEventListener('click', () => {
        goToSlide(3);
    });

        // Copiar el prompt
        copyButton.addEventListener('click', async () => {
            const promptText = document.getElementById('final-prompt').textContent;
            try {
                await navigator.clipboard.writeText(promptText);
                copyButton.textContent = '¡Copiado!';

                // Mostrar el modal personalizado en lugar de abrir Tally directamente
                const tallyModal = document.getElementById('tally-feedback-modal');
                if (tallyModal) {
                    tallyModal.classList.remove('hidden');
                }

                setTimeout(() => {
                    copyButton.textContent = 'Copiar Prompt';
                }, 2000);

                // Comentado el código anterior de Tally
                // Tally.openPopup('n9xMp4', {
                //     width: 372,
                //     emoji: { text: '👋', animation: 'flash' },
                // });
            } catch (err) {
                console.error('Error al copiar:', err);
            }
        });

    // Inicializar
    // Asegurarse de que solo el primer slide esté visible al cargar
    slides.forEach((slide, index) => {
        if (index === 0) {
            slide.style.display = 'flex';
            slide.classList.add('active');
        } else {
            slide.style.display = 'none';
            slide.classList.remove('active');
        }
    });

    updateProgress();
    updateBackButton();
});