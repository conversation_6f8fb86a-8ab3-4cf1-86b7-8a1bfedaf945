
<?php
// Incluir archivos necesarios
require_once './include/language.php';
?>
<!DOCTYPE html>
<html lang="<?php echo $current_language; ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ejemplos - Generador de Prompts IA</title>

    <!-- ⚡ Script INLINE para evitar flash del modo oscuro -->
    <script>
        (function() {
            if (localStorage.getItem('darkMode') === 'enabled' ||
                (localStorage.getItem('darkMode') === null && window.matchMedia('(prefers-color-scheme: dark)').matches)) {
                document.documentElement.classList.add('dark');
            }
        })();
    </script>

    <!-- Script para aplicar el modo oscuro inmediatamente -->
    <script type="module" src="./js/colorMode.js"></script>
    <link rel="stylesheet" href="./css/styles.css">
    <link rel="stylesheet" href="./css/custom.css">

    <script async src="https://tally.so/widgets/embed.js"></script>

    <!-- Scripts de Supabase para autenticación -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js"></script>
    <script src="./js/auth.js"></script>
    <script src="./js/language.js"></script>

</head>
<body class="bg-gray-50 dark:bg-gray-900 min-h-screen">
    <!-- Navegación -->
    <nav class="bg-white shadow-sm sticky top-0 z-10 dark:bg-gray-800">

    <?php  require './include/nav.php';  ?>

        <!-- Menú móvil desplegable -->
     <?php require './include/nav_movil.php';?>
    </nav>

    <div class="container mx-auto px-4 py-8">
        <div class="content">
        <h1 class="text-4xl font-bold text-center mb-2 dark:text-white"><?php echo t('examples_title'); ?></h1>
        <p class="text-gray-600 text-center mb-12 max-w-2xl mx-auto dark:text-gray-300"></p>

        <div class="mb-12">
            <h2 class="text-2xl font-bold mb-6 pb-2 border-b dark:text-white dark:border-gray-600"><?php echo t('examples_coding_section'); ?></h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Ejemplo 1 -->
                <div class="bg-white rounded-xl shadow-md p-5 example-card relative dark:bg-gray-800">
                    <div class="flex justify-between items-start mb-4">
                        <span class="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded">v0.dev</span>
                        <button class="copy-button text-gray-500 hover:text-gray-700">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5H6a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2v-1M8 5a2 2 0 002 2h2a2 2 0 002-2M8 5a2 2 0 012-2h2a2 2 0 012 2m0 0h2a2 2 0 012 2v3m2 4H10m0 0l3-3m-3 3l3 3" />
                            </svg>
                        </button>
                    </div>
                    <h3 class="text-xl font-semibold mb-2 dark:text-white"><?php echo t('example_dashboard_title'); ?></h3>
                    <div class="bg-gray-50 p-3 rounded-lg mb-4 text-sm dark:bg-gray-700">
                        <pre class="whitespace-pre-wrap dark:text-gray-300"><?php echo t('example_dashboard_prompt'); ?></pre>
                    </div>
                    <a href="/" class="text-blue-600 hover:text-blue-800 text-sm font-medium inline-flex items-center dark:text-blue-400 dark:hover:text-blue-300">
                        <?php echo t('use_as_base'); ?>
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3" />
                        </svg>
                    </a>
                </div>

                <!-- Ejemplo 2 -->
                <div class="bg-white rounded-xl shadow-md p-5 example-card relative dark:bg-gray-800">
                    <div class="flex justify-between items-start mb-4">
                        <span class="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded">Lovable.dev</span>
                        <button class="copy-button text-gray-500 hover:text-gray-700">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5H6a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2v-1M8 5a2 2 0 002 2h2a2 2 0 002-2M8 5a2 2 0 012-2h2a2 2 0 012 2m0 0h2a2 2 0 012 2v3m2 4H10m0 0l3-3m-3 3l3 3" />
                            </svg>
                        </button>
                    </div>
                    <h3 class="text-xl font-semibold mb-2 dark:text-white"><?php echo t('example_task_app_title'); ?></h3>
                    <div class="bg-gray-50 p-3 rounded-lg mb-4 text-sm dark:bg-gray-700">
                        <pre class="whitespace-pre-wrap dark:text-gray-300"><?php echo t('example_task_app_prompt'); ?></pre>
                    </div>
                    <a href="/" class="text-blue-600 hover:text-blue-800 text-sm font-medium inline-flex items-center dark:text-blue-400 dark:hover:text-blue-300">
                        <?php echo t('use_as_base'); ?>
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3" />
                        </svg>
                    </a>
                </div>
            </div>
        </div>

        <div class="mb-12">
            <h2 class="text-2xl font-bold mb-6 pb-2 border-b dark:text-white dark:border-gray-600"><?php echo t('examples_writing_section'); ?></h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Ejemplo 3 -->
                <div class="bg-white rounded-xl shadow-md p-5 example-card relative dark:bg-gray-800">
                    <div class="flex justify-between items-start mb-4">
                        <span class="bg-purple-100 text-purple-800 text-xs font-medium px-2.5 py-0.5 rounded">Jasper</span>
                        <button class="copy-button text-gray-500 hover:text-gray-700">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5H6a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2v-1M8 5a2 2 0 002 2h2a2 2 0 002-2M8 5a2 2 0 012-2h2a2 2 0 012 2m0 0h2a2 2 0 012 2v3m2 4H10m0 0l3-3m-3 3l3 3" />
                            </svg>
                        </button>
                    </div>
                    <h3 class="text-xl font-semibold mb-2 dark:text-white"><?php echo t('example_tech_article_title'); ?></h3>
                    <div class="bg-gray-50 p-3 rounded-lg mb-4 text-sm dark:bg-gray-700">
                        <pre class="whitespace-pre-wrap dark:text-gray-300"><?php echo t('example_tech_article_prompt'); ?></pre>
                    </div>
                    <a href="/" class="text-blue-600 hover:text-blue-800 text-sm font-medium inline-flex items-center dark:text-blue-400 dark:hover:text-blue-300">
                        <?php echo t('use_as_base'); ?>
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3" />
                        </svg>
                    </a>
                </div>

                <!-- Ejemplo 4 -->
                <div class="bg-white rounded-xl shadow-md p-5 example-card relative dark:bg-gray-800">
                    <div class="flex justify-between items-start mb-4">
                        <span class="bg-purple-100 text-purple-800 text-xs font-medium px-2.5 py-0.5 rounded">Copy.ai</span>
                        <button class="copy-button text-gray-500 hover:text-gray-700">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5H6a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2v-1M8 5a2 2 0 002 2h2a2 2 0 002-2M8 5a2 2 0 012-2h2a2 2 0 012 2m0 0h2a2 2 0 012 2v3m2 4H10m0 0l3-3m-3 3l3 3" />
                            </svg>

                        </button>
                    </div>
                    <h3 class="text-xl font-semibold mb-2 dark:text-white"><?php echo t('example_email_campaign_title'); ?></h3>
                    <div class="bg-gray-50 p-3 rounded-lg mb-4 text-sm dark:bg-gray-700">
                        <pre class="whitespace-pre-wrap dark:text-gray-300"><?php echo t('example_email_campaign_prompt'); ?></pre>
                    </div>
                    <a href="/" class="text-blue-600 hover:text-blue-800 text-sm font-medium inline-flex items-center dark:text-blue-400 dark:hover:text-blue-300">
                        <?php echo t('use_as_base'); ?>
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3" />
                        </svg>
                    </a>
                </div>
            </div>
        </div>
        </div>
    </div>

    <!-- Footer -->
<?php
require './include/footer.php'; ?>

  <script src="./js/copy.js"></script>

  <script>
    // Pasar las traducciones del modo oscuro al JavaScript
    window.translations = {
        dark_mode_text: <?php echo json_encode(t('dark_mode_text')); ?>,
        light_mode_text: <?php echo json_encode(t('light_mode_text')); ?>
    };

    // Funciones globales para la autenticación (compatibilidad con nav.php)
    function showLoginModal() {
        if (window.authSystem) {
            window.authSystem.showLoginModal();
        }
    }

    function showRegisterModal() {
        if (window.authSystem) {
            window.authSystem.showRegisterModal();
        }
    }

    function logout() {
        if (window.authSystem) {
            window.authSystem.logout();
        }
    }
  </script>
      <script src="./js/movilMenu.js"></script>
</body>
</html>
