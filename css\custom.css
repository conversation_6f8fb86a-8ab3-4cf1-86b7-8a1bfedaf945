/* Estilos para el slider */
.slide-container {
    position: relative;
    overflow: hidden;
    min-height: 500px;
    background-color: white;
}

.dark .slide-container {
    background-color: #1f2937 !important; /* dark-surface */
    border: 1px solid #374151 !important; /* dark-border */
}

.slides {
    display: flex;
    transition: transform 0.5s ease;
    width: 100%;
    position: relative;
}

.slide {
    min-width: 100%;
    padding: 2rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-start;
    transition: opacity 0.3s ease;
    position: relative;
}

.slide:not(.active) {
    opacity: 0.5;
    z-index: 0;
}

.slide.active {
    opacity: 1;
    z-index: 1;
}

/* Estilos para las tarjetas de opciones */
.option-card {
    background-color: white;
    transition: all 0.3s ease;
    cursor: pointer;
}

.dark .option-card {
    background-color: #1f2937 !important; /* dark-surface */
    border-color: #374151 !important; /* dark-border */
}

.option-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.option-card.selected {
    border-color: #3b82f6; /* blue-500 */
    background-color: #eff6ff; /* blue-50 */
}

.dark .option-card.selected {
    border-color: #3b82f6 !important; /* blue-500 */
    background-color: #1e3a8a !important; /* blue-900 */
}

/* Barra de progreso */
.progress-bar {
    position: fixed;
    top: 0;
    left: 0;
    height: 3px;
    background-color: #3b82f6; /* blue-500 */
    width: 0;
    transition: width 0.3s ease;
    z-index: 50;
}

/* Botón de retroceso */
.back-button {
    position: absolute;
    left: 1rem;
    top: 4rem; /* Movido debajo del texto del título */
    width: 2.5rem;
    height: 2.5rem;
    border-radius: 50%;
    background-color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    z-index: 10;
    cursor: pointer;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.dark .back-button {
    background-color: #374151; /* dark-border */
    color: white;
}

/* Botón de retroceso inline (dentro de los slides) */
.back-button-inline {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    margin: 0 auto 1.5rem auto;
    padding: 0.5rem 1rem;
    background-color: #f3f4f6;
    border: 1px solid #d1d5db;
    border-radius: 0.5rem;
    color: #374151;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    width: fit-content;
}

.back-button-inline:hover {
    background-color: #e5e7eb;
    border-color: #9ca3af;
}

.dark .back-button-inline {
    background-color: #374151;
    border-color: #4b5563;
    color: #d1d5db;
}

.dark .back-button-inline:hover {
    background-color: #4b5563;
    border-color: #6b7280;
}

/* Pantalla de carga */
.loading-overlay {
    display: none;
}

.loading-overlay.active {
    display: flex;
}

/* Resultado final */
#final-prompt {
    color: #1f2937; /* gray-800 */
}

.dark #final-prompt {
    color: #f3f4f6; /* dark-text-primary */
}

.dark .bg-gray-50, .dark body {
    background-color: #111827 !important; /* dark-bg */
}

/* Asegurarse de que los textos sean visibles en modo oscuro */
.dark h1, .dark h2, .dark h3 {
    color: white !important;
}

.dark p, .dark label, .dark footer, .dark footer a, .dark footer span, .dark li {
    color: #d1d5db !important; /* dark-text-secondary */
}

/* Corregir el footer en modo oscuro */
.dark footer {
    background-color: #111827 !important; /* dark-bg */
    border-top: 1px solid #374151 !important; /* dark-border */
}

/* Asegurar que el footer ocupe todo el ancho */
footer {
    width: 100vw !important;
    margin-left: calc(-50vw + 50%) !important;
    margin-right: calc(-50vw + 50%) !important;
}

/* Reducir el espacio debajo del checkbox */
.checkbox-container {
    margin-bottom: 0 !important;
    padding-bottom: 0 !important;
}

/* Corregir el color de texto en las páginas */
.dark .content h1, .dark .content h2, .dark .content h3,
.dark h1, .dark h2, .dark h3 {
    color: white !important;
}

.dark .content p, .dark .content li, .dark .content a,
.dark p, .dark li, .dark .text-gray-600, .dark .text-gray-700 {
    color: #d1d5db !important;
}

/* Corregir enlaces en modo oscuro */
.dark a, .dark .text-blue-600, .dark .text-blue-800 {
    color: #60a5fa !important; /* blue-400 */
}

/* Corregir fondos en modo oscuro */
.dark .bg-white, .dark .step-card, .dark .example-card {
    background-color: #1f2937 !important;
    border-color: #374151 !important;
}

/* Corregir colores de fondo específicos */
.dark .bg-gray-50, .dark .bg-blue-50 {
    background-color: #111827 !important;
    border-color: #374151 !important;
}

/* Estilos para el modal de autenticación */
.modal-content {
    max-height: 90vh;
    overflow-y: auto;
}

/* Ajustes para el botón de usuario */
#user-profile-button {
    transition: all 0.2s ease;
}

#user-profile-button:hover {
    opacity: 0.9;
}

/* Estilos para el modo oscuro en componentes de Clerk */
.dark .cl-rootBox,
.dark .cl-card,
.dark .cl-internal-b3fm6y {
    background-color: #1f2937 !important;
    color: #f3f4f6 !important;
}

.dark .cl-headerTitle,
.dark .cl-headerSubtitle,
.dark .cl-formFieldLabel,
.dark .cl-formButtonPrimary,
.dark .cl-formButtonReset,
.dark .cl-socialButtonsBlockButton,
.dark .cl-dividerText,
.dark .cl-footerActionText,
.dark .cl-footerActionLink {
    color: #f3f4f6 !important;
}

.dark .cl-formFieldInput {
    background-color: #374151 !important;
    color: #f3f4f6 !important;
    border-color: #4b5563 !important;
}

.dark .cl-formFieldInput:focus {
    border-color: #60a5fa !important;
}

.dark .cl-formButtonPrimary {
    background-color: #3b82f6 !important;
}

.dark .cl-formButtonPrimary:hover {
    background-color: #2563eb !important;
}

.dark .cl-footerActionLink {
    color: #60a5fa !important;
}

.dark .cl-footerActionLink:hover {
    color: #93c5fd !important;
}

/* Estilos para los botones de autenticación mejorados */
.auth-button {
    transition: all 0.2s ease;
    font-weight: 600;
    border-radius: 0.5rem;
    padding: 0.5rem 1rem;
}

.auth-button:hover {
    transform: translateY(-1px);
}

/* Estilos para el menú desplegable del usuario */
#user-profile-dropdown {
    min-width: 12rem;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.dark #user-profile-dropdown {
    background-color: #1f2937 !important;
    border-color: #374151 !important;
}

/* Avatar del usuario */
#user-avatar, #user-avatar-fallback {
    transition: all 0.2s ease;
}

#user-avatar:hover, #user-avatar-fallback:hover {
    transform: scale(1.05);
}

/* Responsividad para pantallas pequeñas */
@media (max-width: 640px) {
    .auth-button {
        padding: 0.375rem 0.75rem;
        font-size: 0.875rem;
    }

    #user-profile-dropdown {
        right: 0;
        left: auto;
        min-width: 10rem;
        z-index: 1200; /* Asegurar que esté por encima de otros elementos */
    }

    .social-icons {
        display: none !important;
    }

    /* Asegurar z-index correcto para menú móvil */
    #mobile-menu {
        z-index: 1200 !important;
    }
}

/* Mejoras para el logo de Threads */
.social-icons a svg {
    transition: all 0.2s ease;
}

.social-icons a:hover svg {
    transform: scale(1.1);
}

/* Estilos para el botón de perfil */
#user-profile-dropdown-button {
    transition: all 0.2s ease;
}

#user-profile-dropdown-button:hover {
    transform: translateY(-1px);
}

/* Estilos para el indicador de carga de autenticación */
#auth-loading {
    opacity: 0.8;
}

#auth-loading .animate-spin {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

/* Transiciones suaves para los elementos de autenticación */
#auth-buttons, #user-profile-button, #auth-loading {
    transition: opacity 0.3s ease, visibility 0.3s ease;
}

/* Ocultar elementos con transición suave */
.auth-hidden {
    opacity: 0 !important;
    visibility: hidden !important;
    pointer-events: none !important;
}

/* ===== SISTEMA DE HISTORIAL ===== */

/* Contenedor principal del sistema de historial */
.history-system-container {
    position: relative;
    min-height: 700px;
    width: 100%;
    overflow: hidden;
}

/* Caja de historial */
.history-box {
    background: white;
    border-radius: 0.75rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    overflow: hidden;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 5;
    outline: none;
    border: none;
}



/* Modo oscuro para la caja de historial */
.dark .history-box {
    background: #374151;
    border: none;
    outline: none;
}

/* Transición de entrada suave */
.history-box[style*="opacity: 1"] {
    transform: translateX(0);
}

/* Estado colapsado (pequeño y cuadrado a la izquierda) */
.history-collapsed {
    width: 80px;
    height: 80px;
    left: 0;
    top: 0;
    border-radius: 12px;
    cursor: pointer;
    display: none; /* Oculto por defecto hasta que se verifique la autenticación */
    opacity: 0;
}

/* Icono para el estado colapsado */
.history-collapsed .history-header {
    justify-content: center;
    align-items: center;
    padding: 0;
    height: 100%;
    width: 100%;
}

.history-collapsed .history-toggle-btn {
    margin: 0;
    width: 100%;
    height: 100%;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Estado expandido (ocupa el centro) */
.history-expanded {
    width: 100% !important;
    max-width: 600px !important;
    min-height: 600px !important;
    height: auto !important;
    left: 50% !important;
    top: 0 !important;
    transform: translateX(-50%) !important;
    border-radius: 12px !important;
    margin: 0 auto !important;
}

/* Header del historial */
.history-header {
    padding: 1rem;
    border-bottom: 1px solid #e5e7eb;
    display: flex;
    align-items: center;
    justify-content: space-between;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.history-header:hover {
    background-color: #f9fafb;
}

.history-title {
    font-size: 0.875rem;
    font-weight: 600;
    color: #374151;
    margin: 0;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* En estado colapsado, ocultar el título */
.history-collapsed .history-title {
    display: none;
}

/* Botón de toggle */
.history-toggle-btn {
    background: none;
    border: none;
    color: #6b7280;
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 0.25rem;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.history-toggle-btn:hover {
    background-color: #f3f4f6;
    color: #374151;
}

/* Manejo de iconos en el botón de toggle */
.history-collapsed .history-icon {
    display: block;
}

.history-collapsed .expand-arrow {
    display: none;
}

.history-expanded .history-icon {
    display: none;
}

.history-expanded .expand-arrow {
    display: block;
    transform: rotate(180deg);
}

/* Contenido del historial */
.history-content {
    padding: 1rem;
    height: calc(100% - 60px);
    overflow-y: auto;
}

/* En estado colapsado, ocultar el contenido */
.history-collapsed .history-content {
    display: none;
}

/* Lista del historial */
.history-list {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

/* Placeholder cuando no hay historial */
.history-placeholder {
    text-align: center;
    padding: 2rem 1rem;
}

/* Contenedor principal de herramientas */
.main-tools-container {
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    position: absolute;
    top: 0;
    z-index: 10;
    overflow: visible;
}

/* Estado normal (ocupa el centro) */
.main-tools-normal {
    width: 100% !important;
    max-width: 600px !important;
    min-height: 700px !important;
    height: auto !important;
    left: 50% !important;
    top: 0 !important;
    transform: translateX(-50%) !important;
    margin: 0 auto !important;
    padding-bottom: 2rem !important;
    z-index: 5 !important;
}

/* Estado comprimido (pequeño a la derecha) */
.main-tools-compressed {
    width: 80px;
    height: 80px;
    right: 0;
    top: 0;
    overflow: hidden;
    border-radius: 12px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    background: white;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    outline: none;
    border: none;
}

/* Modo oscuro para el contenedor comprimido */
.dark .main-tools-compressed {
    background: #374151;
    border: none;
    outline: none;
}

/* Asegurar que los botones se vean en estado normal */
.main-tools-normal .btn,
.main-tools-normal button {
    position: relative;
    z-index: 20;
}

/* Asegurar que el contenido del formulario se vea correctamente */
.main-tools-normal .slide-content {
    overflow: visible;
    position: relative;
    z-index: 15;
}

/* Eliminar cualquier outline o borde durante las transiciones */
.history-box:focus,
.main-tools-container:focus,
.main-tools-compressed:focus,
.history-collapsed:focus {
    outline: none !important;
}

/* Arreglar el borde blanco en modo oscuro específicamente */
.dark .history-box {
    border: 1px solid #4B5563 !important;
}

.dark .main-tools-compressed {
    border: 1px solid #4B5563 !important;
}

/* Asegurar transiciones suaves sin bordes blancos */
.history-box,
.main-tools-container {
    border: 1px solid transparent;
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1), border-color 0.3s ease;
}

/* Mejorar la interacción del selector de idioma */
#language-selector {
    cursor: pointer;
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    position: relative;
    z-index: 1000;
}

#language-selector:hover {
    background-color: rgba(59, 130, 246, 0.1);
    border-radius: 0.375rem;
    padding: 0.25rem 0.5rem;
    margin: -0.25rem -0.5rem;
}

#language-dropdown {
    pointer-events: auto;
    user-select: none;
    -webkit-user-select: none;
}

#language-dropdown button {
    cursor: pointer;
    transition: background-color 0.2s ease;
}

/* Ocultar contenido cuando está comprimido */
.main-tools-compressed > *:not(.compressed-icon) {
    display: none;
}

/* Ocultar el botón de historial cuando la caja está comprimida */
.main-tools-compressed .tools-history-btn {
    display: none !important;
}

/* Mostrar solo el ícono cuando está comprimido */
.main-tools-compressed .compressed-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    font-size: 1.5rem;
    color: #6b7280;
}

/* Botón de historial integrado en la caja de herramientas */
.tools-history-btn {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: rgba(255, 255, 255, 0.9);
    border: 1px solid #e5e7eb;
    border-radius: 0.5rem;
    padding: 0.5rem;
    color: #6b7280;
    cursor: pointer;
    transition: all 0.2s ease;
    z-index: 20;
    backdrop-filter: blur(4px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    display: none; /* Oculto por defecto hasta que se verifique la autenticación */
}

.tools-history-btn:hover {
    background: rgba(255, 255, 255, 1);
    color: #374151;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

/* Modo oscuro para el botón de historial */
.dark .tools-history-btn {
    background: rgba(31, 41, 55, 0.9);
    border-color: #4b5563;
    color: #9ca3af;
}

.dark .tools-history-btn:hover {
    background: rgba(31, 41, 55, 1);
    color: #f9fafb;
}

/* En desktop: mostrar botón externo grande, ocultar integrado */
@media (min-width: 769px) {
    .tools-history-btn {
        display: none !important;
    }

    .history-collapsed {
        display: block !important;
    }
}

/* En móvil: mostrar botón integrado pequeño, ocultar externo */
@media (max-width: 768px) {
    .history-collapsed {
        display: none !important;
    }

    /* Botón de historial en móvil - oculto por defecto */
    .tools-history-btn {
        position: fixed !important;
        top: 4.5rem !important;
        left: 0.75rem !important;
        right: auto !important;
        padding: 0.5rem !important;
        background: #3b82f6 !important;
        color: white !important;
        border-radius: 50% !important;
        width: 40px !important;
        height: 40px !important;
        display: none; /* Oculto por defecto, sin !important para permitir override */
        align-items: center !important;
        justify-content: center !important;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3) !important;
        z-index: 1100 !important;
    }

    /* Clase para mostrar el botón cuando el usuario está autenticado */
    .tools-history-btn.show {
        display: flex !important;
    }

    .tools-history-btn svg {
        width: 0.875rem;
        height: 0.875rem;
    }
}

/* Modo oscuro */
.dark .history-box {
    background: #1f2937;
    border-color: #374151;
}

.dark .main-tools-compressed {
    background: #1f2937;
    border: 1px solid #374151;
}

.dark .main-tools-compressed .compressed-icon {
    color: #9ca3af;
}

.dark .history-header {
    border-color: #374151;
}

.dark .history-header:hover {
    background-color: #374151;
}

.dark .history-title {
    color: #f9fafb;
}

.dark .history-toggle-btn {
    color: #9ca3af;
}

.dark .history-toggle-btn:hover {
    background-color: #4b5563;
    color: #f9fafb;
}

/* Responsive - Mantener el mismo sistema de slides en móvil */
@media (max-width: 768px) {
    /* Contenedor principal - permitir scroll vertical */
    .history-system-container {
        position: relative !important;
        width: 100% !important;
        max-width: 100% !important;
        min-height: calc(100vh - 200px) !important;
        overflow-x: hidden !important;
        padding-bottom: 2rem !important;
        overflow-y: visible;
        padding: 0.5rem;
    }

    /* Caja de historial en móvil - misma lógica pero adaptada */
    .history-collapsed {
        width: 60px;
        height: 60px;
        left: 0;
        top: 0;
        border-radius: 12px;
        cursor: pointer;
        position: absolute;
        z-index: 10;
    }



    /* Contenedor principal de herramientas en móvil - COMPLETAMENTE ARREGLADO */
    .main-tools-normal {
        width: 100% !important;
        max-width: none !important;
        min-height: auto !important;
        height: auto !important;
        left: 0 !important;
        top: 0 !important;
        transform: translateX(0) !important;
        margin: 0 !important;
        padding: 1rem !important;
        background: white !important;
        border-radius: 0.75rem !important;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;
        position: relative !important;
    }

    /* Modo oscuro para móvil */
    .dark .main-tools-normal {
        background: #374151 !important;
        border: 1px solid #4B5563 !important;
    }

    .main-tools-compressed {
        width: 60px;
        height: 60px;
        right: 0;
        top: 0;
        border-radius: 12px;
        position: absolute;
        z-index: 10;
    }

    /* Ajustar el contenido del historial en móvil */
    .history-collapsed .history-title {
        display: none;
    }

    .history-collapsed .history-header {
        justify-content: center;
        align-items: center;
        padding: 0;
        height: 100%;
        width: 100%;
    }

    .history-expanded .history-content {
        display: block;
        height: calc(100% - 60px);
        overflow-y: auto;
    }

    /* Asegurar que el texto y botones se vean bien en móvil */
    .main-tools-normal h1 {
        font-size: 1.875rem !important;
        line-height: 2.25rem !important;
    }

    .main-tools-normal .typewriter {
        font-size: 1.125rem !important;
    }

    /* Asegurar que el contenido del slider se vea correctamente */
    .main-tools-normal .slide-content {
        padding: 1rem;
        max-width: 100%;
    }

    /* Ajustar botones en móvil */
    .main-tools-normal .btn,
    .main-tools-normal button {
        font-size: 0.875rem;
        padding: 0.75rem 1.5rem;
    }

    /* Ajustar inputs en móvil */
    .main-tools-normal input,
    .main-tools-normal textarea,
    .main-tools-normal select {
        font-size: 1rem;
        padding: 0.75rem;
    }

    /* Asegurar que las opciones de herramientas se vean bien */
    .main-tools-normal .tool-option {
        padding: 1rem;
        margin-bottom: 0.5rem;
    }

    /* Ajustar el contenedor para que no se corte */
    .container {
        padding-left: 0.5rem;
        padding-right: 0.5rem;
    }

    /* Asegurar que el footer no interfiera */
    body {
        padding-bottom: 2rem;
    }

    /* Ajustar el contenedor principal de la página - MEJORADO */
    .min-h-screen,
    .min-h-\[calc\(100vh-4rem\)\] {
        min-height: auto !important;
    }

    /* Asegurar que el historial colapsado sea visible */
    .history-collapsed .history-icon {
        width: 1.5rem;
        height: 1.5rem;
    }

    /* Mejorar scroll en móvil para las cajas de herramientas - OPTIMIZADO */
    .slide-content {
        max-height: 70vh;
        overflow-y: auto;
        -webkit-overflow-scrolling: touch;
        padding-right: 8px;
    }

    .tools-grid {
        max-height: 60vh;
        overflow-y: auto;
        -webkit-overflow-scrolling: touch;
        padding-right: 8px;
    }

    /* Permitir scroll en el contenido de historial - MEJORADO */
    .history-content {
        max-height: 70vh;
        overflow-y: auto;
        -webkit-overflow-scrolling: touch;
        padding-right: 8px;
    }

    /* Asegurar que el slide-container no tenga altura fija problemática */
    .slide-container {
        min-height: auto !important;
        height: auto !important;
    }

    /* Mejorar el espaciado del contenido principal */
    .main-tools-normal .max-w-2xl {
        max-width: 100% !important;
        padding: 0 !important;
    }

}
