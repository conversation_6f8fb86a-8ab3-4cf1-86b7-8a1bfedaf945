<?php
/**
 * API para manejar el historial de prompts
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Manejar preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/../utils/jwt_helper.php';

/**
 * Obtener user_id interno de la base de datos usando el JWT de Supabase verificado
 */
function getUserIdFromJWT($token) {
    global $pdo;

    try {
        // Usar la nueva utilidad JWT para verificar y decodificar el token
        $userData = JWTHelper::verifyAndDecodeToken($token);

        if (!$userData || !isset($userData['user_id'])) {
            return null;
        }

        $supabaseUserId = $userData['user_id'];

        // Buscar el ID interno del usuario en la base de datos
        $stmt = $pdo->prepare("SELECT id FROM users WHERE supabase_user_id = ?");
        $stmt->execute([$supabaseUserId]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);

        return $user ? $user['id'] : null;

    } catch (Exception $e) {
        error_log('Error en getUserIdFromJWT: ' . $e->getMessage());
        return null;
    }
}

// Verificar que el usuario esté autenticado con JWT
$headers = getallheaders();
$authHeader = $headers['Authorization'] ?? '';

if (empty($authHeader) || !str_starts_with($authHeader, 'Bearer ')) {
    http_response_code(401);
    echo json_encode(['error' => 'Token de autorización requerido']);
    exit();
}

$token = substr($authHeader, 7); // Remover "Bearer "

// Decodificar JWT para obtener user_id
$userId = getUserIdFromJWT($token);
if (!$userId) {
    http_response_code(401);
    echo json_encode(['error' => 'Token inválido']);
    exit();
}

// Manejar diferentes métodos HTTP
$method = $_SERVER['REQUEST_METHOD'];

switch ($method) {
    case 'GET':
        getHistory($userId);
        break;
    case 'POST':
        saveToHistory($userId);
        break;
    case 'DELETE':
        deleteFromHistory($userId);
        break;
    default:
        http_response_code(405);
        echo json_encode(['error' => 'Método no permitido']);
        break;
}

/**
 * Obtener historial del usuario
 */
function getHistory($userId) {
    global $pdo;

    $limit = $_GET['limit'] ?? 20;
    $offset = $_GET['offset'] ?? 0;

    try {
        $stmt = $pdo->prepare("
            SELECT id, tool_type, tool_name, prompt_title, prompt_content, form_data, created_at
            FROM prompt_history
            WHERE user_id = ?
            ORDER BY created_at DESC
            LIMIT ? OFFSET ?
        ");

        $stmt->execute([$userId, (int)$limit, (int)$offset]);
        $history = $stmt->fetchAll(PDO::FETCH_ASSOC);

        echo json_encode([
            'success' => true,
            'data' => $history
        ]);

    } catch (PDOException $e) {
        http_response_code(500);
        echo json_encode(['error' => 'Error al obtener historial: ' . $e->getMessage()]);
    }
}

/**
 * Guardar nuevo prompt en el historial
 */
function saveToHistory($userId) {
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        http_response_code(400);
        echo json_encode(['error' => 'Datos inválidos']);
        return;
    }

    $data = [
        'user_id' => $userId,
        'tool_type' => $input['tool_type'] ?? '',
        'tool_name' => $input['tool_name'] ?? '',
        'prompt_title' => $input['prompt_title'] ?? '',
        'prompt_content' => $input['prompt_content'] ?? '',
        'form_data' => $input['form_data'] ?? null
    ];

    // Validar datos requeridos
    if (empty($data['tool_type']) || empty($data['tool_name']) || empty($data['prompt_content'])) {
        http_response_code(400);
        echo json_encode(['error' => 'Faltan datos requeridos']);
        return;
    }

    try {
        global $pdo;

        $stmt = $pdo->prepare("
            INSERT INTO prompt_history (user_id, tool_type, tool_name, prompt_title, prompt_content, form_data)
            VALUES (?, ?, ?, ?, ?, ?)
        ");

        $formDataJson = isset($input['form_data']) ? json_encode($input['form_data']) : null;

        // Validar que tool_type sea válido para el ENUM
        $validToolTypes = ['coding', 'writing'];
        $toolType = in_array($input['tool_type'], $validToolTypes) ? $input['tool_type'] : 'coding';

        $stmt->execute([
            $userId,
            $toolType,
            $input['tool_name'],
            $input['prompt_title'] ?? null,
            $input['prompt_content'],
            $formDataJson
        ]);

        echo json_encode(['success' => true, 'id' => $pdo->lastInsertId()]);

    } catch (PDOException $e) {
        http_response_code(500);
        echo json_encode(['error' => 'Error al guardar en historial: ' . $e->getMessage()]);
    }
}

/**
 * Eliminar prompt del historial
 */
function deleteFromHistory($userId) {
    global $pdo;

    $input = json_decode(file_get_contents('php://input'), true);
    $historyId = $input['id'] ?? null;

    if (!$historyId) {
        http_response_code(400);
        echo json_encode(['error' => 'ID requerido']);
        return;
    }

    try {
        $stmt = $pdo->prepare("DELETE FROM prompt_history WHERE id = ? AND user_id = ?");
        $stmt->execute([$historyId, $userId]);

        if ($stmt->rowCount() > 0) {
            echo json_encode(['success' => true]);
        } else {
            http_response_code(404);
            echo json_encode(['error' => 'Prompt no encontrado']);
        }

    } catch (PDOException $e) {
        http_response_code(500);
        echo json_encode(['error' => 'Error al eliminar del historial: ' . $e->getMessage()]);
    }
}
?>
