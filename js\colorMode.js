// ⚡ APLICAR MODO OSCURO INMEDIATAMENTE (antes de que se cargue el DOM)
(function() {
    const html = document.documentElement;

    // Aplicar modo oscuro inmediatamente si estaba guardado
    if (localStorage.getItem('darkMode') === 'enabled') {
        html.classList.add('dark');
    } else if (localStorage.getItem('darkMode') === null) {
        // Si no hay preferencia guardada, usar la del sistema
        const prefersDarkScheme = window.matchMedia('(prefers-color-scheme: dark)');
        if (prefersDarkScheme.matches) {
            html.classList.add('dark');
        }
    }
})();

// Esperar a que el DOM esté completamente cargado para configurar el botón
document.addEventListener('DOMContentLoaded', function() {
    // Dark mode toggle functionality
    const darkModeToggle = document.getElementById('toggle-dark-mode');
    const mobileDarkModeToggle = document.getElementById('mobile-toggle-dark-mode');
    const html = document.documentElement;

    // Función para aplicar el modo oscuro
    function enableDarkMode() {
        if (html) {
            html.classList.add('dark');
            const lightModeText = window.translations?.light_mode_text || 'Modo Claro';

            if (darkModeToggle) {
                darkModeToggle.textContent = lightModeText;
                darkModeToggle.classList.remove('bg-gray-200', 'text-gray-800');
                darkModeToggle.classList.add('bg-gray-700', 'text-white');
            }
            if (mobileDarkModeToggle) {
                mobileDarkModeToggle.textContent = lightModeText;
                mobileDarkModeToggle.classList.remove('bg-gray-100', 'text-gray-800');
                mobileDarkModeToggle.classList.add('bg-gray-600', 'text-white');
            }
            localStorage.setItem('darkMode', 'enabled');
        }
    }

    // Función para desactivar el modo oscuro
    function disableDarkMode() {
        if (html) {
            html.classList.remove('dark');
            const darkModeText = window.translations?.dark_mode_text || 'Modo Oscuro';

            if (darkModeToggle) {
                darkModeToggle.textContent = darkModeText;
                darkModeToggle.classList.remove('bg-gray-700', 'text-white');
                darkModeToggle.classList.add('bg-gray-200', 'text-gray-800');
            }
            if (mobileDarkModeToggle) {
                mobileDarkModeToggle.textContent = darkModeText;
                mobileDarkModeToggle.classList.remove('bg-gray-600', 'text-white');
                mobileDarkModeToggle.classList.add('bg-gray-100', 'text-gray-800');
            }
            localStorage.setItem('darkMode', 'disabled');
        }
    }

    // Actualizar los botones según el estado actual
    const darkModeText = window.translations?.dark_mode_text || 'Modo Oscuro';
    const lightModeText = window.translations?.light_mode_text || 'Modo Claro';

    if (html.classList.contains('dark')) {
        if (darkModeToggle) {
            darkModeToggle.textContent = lightModeText;
            darkModeToggle.classList.remove('bg-gray-200', 'text-gray-800');
            darkModeToggle.classList.add('bg-gray-700', 'text-white');
        }
        if (mobileDarkModeToggle) {
            mobileDarkModeToggle.textContent = lightModeText;
            mobileDarkModeToggle.classList.remove('bg-gray-100', 'text-gray-800');
            mobileDarkModeToggle.classList.add('bg-gray-600', 'text-white');
        }
    } else {
        if (darkModeToggle) {
            darkModeToggle.textContent = darkModeText;
            darkModeToggle.classList.remove('bg-gray-700', 'text-white');
            darkModeToggle.classList.add('bg-gray-200', 'text-gray-800');
        }
        if (mobileDarkModeToggle) {
            mobileDarkModeToggle.textContent = darkModeText;
            mobileDarkModeToggle.classList.remove('bg-gray-600', 'text-white');
            mobileDarkModeToggle.classList.add('bg-gray-100', 'text-gray-800');
        }
    }

    // Toggle dark mode - Desktop
    if (darkModeToggle) {
        darkModeToggle.addEventListener('click', function() {
            if (html.classList.contains('dark')) {
                disableDarkMode();
            } else {
                enableDarkMode();
            }
        });
    }

    // Toggle dark mode - Mobile
    if (mobileDarkModeToggle) {
        mobileDarkModeToggle.addEventListener('click', function() {
            if (html.classList.contains('dark')) {
                disableDarkMode();
            } else {
                enableDarkMode();
            }
            // Cerrar el menú móvil después de cambiar el modo
            const mobileMenu = document.getElementById('mobile-menu');
            if (mobileMenu) {
                mobileMenu.classList.add('hidden');
            }
        });
    }
});
