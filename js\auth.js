/**
 * Sistema de autenticación universal con Supabase
 */

// Configuración de Supabase
const SUPABASE_URL = 'https://sjthrdutwnqnvwrwhngc.supabase.co';
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InNqdGhyZHV0d25xbnZ3cndobmdjIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk1OTc0MDMsImV4cCI6MjA2NTE3MzQwM30.4H99rgVg65cwRheSWOe2BGt7-DAiKeGuxzTP-h31lwQ';

// Inicializar cliente de Supabase
const supabase = window.supabase.createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

// Estado global de autenticación
let currentUser = null;
let isAuthInitialized = false;

/**
 * Inicializar el sistema de autenticación
 */
async function initAuth() {
    if (isAuthInitialized) return;

    // Timeout de seguridad para ocultar el indicador de carga
    const loadingTimeout = setTimeout(() => {
        console.log('⏰ Timeout de carga alcanzado, mostrando botones de auth');
        updateUIForUnauthenticatedUser();
    }, 3000); // 3 segundos máximo

    try {
        // Obtener sesión actual
        const { data: { session }, error } = await supabase.auth.getSession();

        // Limpiar el timeout ya que obtuvimos respuesta
        clearTimeout(loadingTimeout);

        if (error) {
            console.error('Error getting session:', error);
            updateUIForUnauthenticatedUser();
            return;
        }

        if (session?.user) {
            currentUser = session.user;
            await updateUIForAuthenticatedUser(currentUser);
        } else {
            updateUIForUnauthenticatedUser();
        }

        // Escuchar cambios de autenticación
        supabase.auth.onAuthStateChange(async (event, session) => {
            console.log('🔄 Auth state changed:', event);
            console.log('👤 Session data:', session);

            if (event === 'SIGNED_IN' && session?.user) {
                currentUser = session.user;
                console.log('✅ Usuario autenticado:', currentUser);
                await updateUIForAuthenticatedUser(currentUser);

                // Notificar al sistema de historial que el usuario está autenticado
                document.dispatchEvent(new CustomEvent('authStateChanged', {
                    detail: { isAuthenticated: true, user: currentUser }
                }));

            } else if (event === 'SIGNED_OUT') {
                currentUser = null;
                console.log('❌ Usuario desautenticado');
                updateUIForUnauthenticatedUser();

                // Notificar al sistema de historial que el usuario cerró sesión
                document.dispatchEvent(new CustomEvent('authStateChanged', {
                    detail: { isAuthenticated: false, user: null }
                }));
            }
        });

        isAuthInitialized = true;
    } catch (error) {
        console.error('Error initializing auth:', error);
        clearTimeout(loadingTimeout);
        updateUIForUnauthenticatedUser();
    }
}

/**
 * Actualizar UI para usuario autenticado
 */
async function updateUIForAuthenticatedUser(user) {
    try {
        const authLoading = document.getElementById('auth-loading');
        const authButtons = document.getElementById('auth-buttons');
        const userProfileButton = document.getElementById('user-profile-button');

        // Ocultar indicador de carga con transición
        if (authLoading) {
            authLoading.classList.add('auth-hidden');
            setTimeout(() => {
                authLoading.style.display = 'none';
                authLoading.classList.add('hidden');
            }, 300);
        }

        // Ocultar botones de autenticación con transición
        if (authButtons) {
            authButtons.classList.add('auth-hidden');
            setTimeout(() => {
                authButtons.style.display = 'none';
                authButtons.classList.add('hidden');
            }, 300);
        }

        // Mostrar botón de perfil con transición
        if (userProfileButton) {
            userProfileButton.style.display = 'flex';
            userProfileButton.classList.remove('hidden');
            // Pequeño delay para permitir que el display se aplique
            setTimeout(() => {
                userProfileButton.classList.remove('auth-hidden');
            }, 50);
        }

        // Actualizar información del usuario
        updateUserInfo(user);

        // Configurar menú desplegable
        setupUserDropdown();

        // Notificar al sistema de historial que el usuario está autenticado
        document.dispatchEvent(new CustomEvent('authStateChanged', {
            detail: { isAuthenticated: true, user: user }
        }));

    } catch (error) {
        console.error('Error updating UI for authenticated user:', error);
    }
}

/**
 * Actualizar UI para usuario no autenticado
 */
function updateUIForUnauthenticatedUser() {
    const authLoading = document.getElementById('auth-loading');
    const authButtons = document.getElementById('auth-buttons');
    const userProfileButton = document.getElementById('user-profile-button');

    // Ocultar indicador de carga con transición
    if (authLoading) {
        authLoading.classList.add('auth-hidden');
        setTimeout(() => {
            authLoading.style.display = 'none';
            authLoading.classList.add('hidden');
        }, 300);
    }

    // Mostrar botones de autenticación con transición
    if (authButtons) {
        authButtons.style.display = 'flex';
        authButtons.classList.remove('hidden');
        // Pequeño delay para permitir que el display se aplique
        setTimeout(() => {
            authButtons.classList.remove('auth-hidden');
        }, 50);
    }

    // Ocultar botón de perfil con transición
    if (userProfileButton) {
        userProfileButton.classList.add('auth-hidden');
        setTimeout(() => {
            userProfileButton.style.display = 'none';
            userProfileButton.classList.add('hidden');
        }, 300);
    }

    // Notificar al sistema de historial que el usuario no está autenticado
    document.dispatchEvent(new CustomEvent('authStateChanged', {
        detail: { isAuthenticated: false, user: null }
    }));
 }

/**
 * Actualizar información del usuario en la UI
 */
function updateUserInfo(user) {
    const userName = document.getElementById('user-name');
    const userAvatar = document.getElementById('user-avatar');
    const userAvatarFallback = document.getElementById('user-avatar-fallback');
    const dropdownUserName = document.getElementById('dropdown-user-name');
    const dropdownUserEmail = document.getElementById('dropdown-user-email');
    const dropdownUserAvatar = document.getElementById('dropdown-user-avatar');
    const dropdownUserAvatarFallback = document.getElementById('dropdown-user-avatar-fallback');

    // Debug temporal para imágenes
    console.log('🔍 Datos del usuario:', user);
    console.log('🔍 Metadatos del usuario:', user.user_metadata);

    // Obtener nombre y email
    const displayName = user.user_metadata?.full_name ||
                       user.user_metadata?.name ||
                       user.email?.split('@')[0] ||
                       'Usuario';

    const email = user.email || '';

    // Buscar la URL del avatar en diferentes ubicaciones posibles
    const avatarUrl = user.user_metadata?.avatar_url ||
                     user.user_metadata?.picture ||
                     user.user_metadata?.photo ||
                     user.identities?.[0]?.identity_data?.avatar_url ||
                     user.identities?.[0]?.identity_data?.picture;

    console.log('🖼️ URL del avatar encontrada:', avatarUrl);
    console.log('🖼️ Elementos encontrados:', {
        userAvatar: !!userAvatar,
        dropdownUserAvatar: !!dropdownUserAvatar,
        userAvatarFallback: !!userAvatarFallback,
        dropdownUserAvatarFallback: !!dropdownUserAvatarFallback
    });

    // Actualizar nombre
    if (userName) userName.textContent = displayName;
    if (dropdownUserName) dropdownUserName.textContent = displayName;
    if (dropdownUserEmail) dropdownUserEmail.textContent = email;

    // Sincronizar usuario con la base de datos y cargar créditos
    syncUserWithDatabase(user);

    // Función para mostrar fallback
    function showAvatarFallback(name, avatarElement, fallbackElement) {
        if (fallbackElement) {
            const initials = name.split(' ').map(n => n[0]).join('').toUpperCase().slice(0, 2);
            fallbackElement.textContent = initials;
            fallbackElement.style.display = 'flex';
        }
        if (avatarElement) {
            avatarElement.style.display = 'none';
        }
    }

    // Actualizar avatar principal
    if (avatarUrl && userAvatar) {
        console.log('🖼️ Cargando avatar principal:', avatarUrl);

        userAvatar.onload = function() {
            console.log('✅ Avatar principal cargado exitosamente');
            userAvatar.style.display = 'block';
            if (userAvatarFallback) userAvatarFallback.style.display = 'none';
        };

        userAvatar.onerror = function() {
            console.log('❌ Error al cargar avatar principal');
            userAvatar.style.display = 'none';
            showAvatarFallback(displayName, userAvatar, userAvatarFallback);
        };

        // Configurar la imagen
        userAvatar.src = avatarUrl;
        userAvatar.alt = displayName;

    } else {
        console.log('📝 Sin avatar URL, usando fallback para botón principal');
        showAvatarFallback(displayName, userAvatar, userAvatarFallback);
    }

    // Actualizar avatar del dropdown
    if (avatarUrl && dropdownUserAvatar) {
        console.log('🖼️ Cargando avatar dropdown:', avatarUrl);

        dropdownUserAvatar.onload = function() {
            console.log('✅ Avatar dropdown cargado exitosamente');
            dropdownUserAvatar.style.display = 'block';
            if (dropdownUserAvatarFallback) dropdownUserAvatarFallback.style.display = 'none';
        };

        dropdownUserAvatar.onerror = function() {
            console.log('❌ Error al cargar avatar dropdown');
            dropdownUserAvatar.style.display = 'none';
            showAvatarFallback(displayName, dropdownUserAvatar, dropdownUserAvatarFallback);
        };

        // Configurar la imagen
        dropdownUserAvatar.src = avatarUrl;
        dropdownUserAvatar.alt = displayName;

    } else {
        console.log('📝 Sin avatar URL, usando fallback para dropdown');
        showAvatarFallback(displayName, dropdownUserAvatar, dropdownUserAvatarFallback);
    }
}

/**
 * Configurar menú desplegable del usuario
 */
function setupUserDropdown() {
    const dropdownButton = document.getElementById('user-profile-dropdown-button');
    const dropdown = document.getElementById('user-profile-dropdown');

    if (!dropdownButton || !dropdown) return;

    // Remover event listeners existentes para evitar duplicados
    const newDropdownButton = dropdownButton.cloneNode(true);
    dropdownButton.parentNode.replaceChild(newDropdownButton, dropdownButton);

    // Configurar nuevo event listener para el botón
    newDropdownButton.addEventListener('click', (e) => {
        e.stopPropagation();
        dropdown.classList.toggle('hidden');

        // Animar la flecha
        const arrow = newDropdownButton.querySelector('#dropdown-arrow');
        if (arrow) {
            if (dropdown.classList.contains('hidden')) {
                arrow.style.transform = 'rotate(0deg)';
            } else {
                arrow.style.transform = 'rotate(180deg)';
            }
        }
    });

    // Remover event listener global existente y agregar uno nuevo
    if (window.dropdownClickHandler) {
        document.removeEventListener('click', window.dropdownClickHandler);
    }

    window.dropdownClickHandler = (e) => {
        if (!dropdown.contains(e.target) && !newDropdownButton.contains(e.target)) {
            dropdown.classList.add('hidden');

            // Resetear la flecha
            const arrow = newDropdownButton.querySelector('#dropdown-arrow');
            if (arrow) {
                arrow.style.transform = 'rotate(0deg)';
            }
        }
    };

    document.addEventListener('click', window.dropdownClickHandler);
}

/**
 * Mostrar modal de login
 */
async function showLoginModal() {
    try {
        const { data, error } = await supabase.auth.signInWithOAuth({
            provider: 'google',
            options: {
                redirectTo: window.location.origin + window.location.pathname
            }
        });

        if (error) {
            console.error('Error during login:', error);
            showErrorModal('Error al iniciar sesión', error.message);
        }
    } catch (error) {
        console.error('Login error:', error);
        showErrorModal('Error al iniciar sesión', 'Ha ocurrido un error inesperado.');
    }
}

/**
 * Mostrar modal de registro
 */
async function showRegisterModal() {
    // Para OAuth, el registro es igual que el login
    await showLoginModal();
}

/**
 * Sincronizar usuario con la base de datos
 */
async function syncUserWithDatabase(user) {
    try {
        // Extraer información del usuario
        const userData = {
            user_id: user.id,
            email: user.email,
            full_name: user.user_metadata?.full_name || user.user_metadata?.name || null,
            avatar_url: user.user_metadata?.avatar_url ||
                       user.user_metadata?.picture ||
                       user.identities?.[0]?.identity_data?.avatar_url ||
                       user.identities?.[0]?.identity_data?.picture || null
        };

        const response = await fetch('api/sync_user.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(userData)
        });

        const data = await response.json();

        if (data.success) {
            // Actualizar créditos en la UI
            updateCreditsInNav(data.user.credits);
        } else {
            console.error('Error al sincronizar usuario:', data.message);
        }

    } catch (error) {
        console.error('Error al sincronizar usuario:', error);
    }
}

/**
 * Cargar créditos del usuario
 */
async function loadUserCredits(userId) {
    try {
        const response = await fetch('api/get_user_credits.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                user_id: userId
            })
        });

        const data = await response.json();

        if (data.success) {
            updateCreditsInNav(data.credits);
        }

    } catch (error) {
        console.error('Error al cargar créditos:', error);
    }
}

/**
 * Actualizar créditos en la navegación
 */
function updateCreditsInNav(credits) {
    const navCredits = document.getElementById('nav-user-credits');
    if (navCredits) {
        navCredits.textContent = credits.toLocaleString();
    }
}

/**
 * Cerrar sesión
 */
async function logout() {
    try {
        const { error } = await supabase.auth.signOut();
        
        if (error) {
            console.error('Error during logout:', error);
            showErrorModal('Error al cerrar sesión', error.message);
        } else {
            // Recargar la página para limpiar el estado
            window.location.reload();
        }
    } catch (error) {
        console.error('Logout error:', error);
        showErrorModal('Error al cerrar sesión', 'Ha ocurrido un error inesperado.');
    }
}

/**
 * Mostrar modal de error
 */
function showErrorModal(title, message) {
    // Crear modal dinámicamente si no existe
    let modal = document.getElementById('error-modal');
    if (!modal) {
        modal = document.createElement('div');
        modal.id = 'error-modal';
        modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden';
        modal.innerHTML = `
            <div class="bg-white dark:bg-gray-800 rounded-lg p-6 max-w-md mx-4">
                <h3 id="error-modal-title" class="text-lg font-semibold text-gray-900 dark:text-white mb-2"></h3>
                <p id="error-modal-message" class="text-gray-600 dark:text-gray-300 mb-4"></p>
                <button onclick="hideErrorModal()" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors">
                    Entendido
                </button>
            </div>
        `;
        document.body.appendChild(modal);
    }

    document.getElementById('error-modal-title').textContent = title;
    document.getElementById('error-modal-message').textContent = message;
    modal.classList.remove('hidden');
}

/**
 * Ocultar modal de error
 */
function hideErrorModal() {
    const modal = document.getElementById('error-modal');
    if (modal) {
        modal.classList.add('hidden');
    }
}

/**
 * Obtener usuario actual
 */
function getCurrentUser() {
    return currentUser;
}

/**
 * Verificar si el usuario está autenticado
 */
function isAuthenticated() {
    return currentUser !== null;
}

// Inicializar cuando el DOM esté listo
document.addEventListener('DOMContentLoaded', initAuth);

// Exponer funciones globalmente
window.authSystem = {
    initAuth,
    showLoginModal,
    showRegisterModal,
    logout,
    getCurrentUser,
    isAuthenticated,
    showErrorModal,
    hideErrorModal
};
