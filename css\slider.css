/* Estilos para el slider */
.slide-container {
    overflow: hidden;
    position: relative;
    min-height: 450px;
    background-color: white;
}

.dark .slide-container {
    background-color: #1f2937;
}

.slides {
    display: flex;
    height: 100%;
    transition: transform 0.5s ease;
}

.slide {
    min-width: 100%;
    padding: 1.5rem;
    opacity: 1; /* Todos los slides son visibles por defecto */
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    position: relative;
}

.slide.active {
    z-index: 5;
}

/* Mejorar la transición */
.slides {
    will-change: transform;
}

/* Estilos para el botón de volver incrustado */
.back-button-inline {
    display: flex;
    align-items: center;
    padding: 0.5rem 1rem;
    background-color: #3b82f6;
    color: white;
    border-radius: 0.5rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    margin-bottom: 1rem;
    width: fit-content;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    position: relative; /* Volvemos a la posición relativa para desktop */
    z-index: 10;
}

.back-button-inline svg {
    width: 20px;
    height: 20px;
    margin-right: 0.25rem;
}

.back-button-inline:hover {
    background-color: #2563eb;
    transform: translateY(-2px);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.dark .back-button-inline {
    background-color: #4b5563;
    color: #e5e7eb;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.dark .back-button-inline:hover {
    background-color: #6b7280;
}

/* Ajustes para dispositivos móviles - UNIFICADO A 768px */
@media (max-width: 768px) {
    .slide-container {
        min-height: auto;
        position: relative;
        height: auto;
    }

    .slide {
        padding: 1rem 0.5rem;
    }

    /* Botón móvil fijo en la esquina superior izquierda - MEJORADO */
    .mobile-back-button {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 40px;
        height: 40px;
        background-color: #3b82f6;
        color: white;
        border-radius: 50%;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s ease;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
        position: fixed;
        top: 4.5rem; /* Bajado para no tapar el logo de FastlyAI */
        left: 0.75rem;
        z-index: 1100; /* Aumentado para evitar conflictos */
    }

    .mobile-back-button svg {
        width: 22px;
        height: 22px;
    }

    .mobile-back-button:hover {
        background-color: #2563eb;
    }

    .dark .mobile-back-button {
        background-color: #6366f1;
        box-shadow: 0 3px 6px rgba(0, 0, 0, 0.4);
    }

    /* Reposicionamos el botón normal en móviles para que no tape las letras */
    .back-button-inline {
        position: relative;
        margin-top: 1rem; /* Más espacio desde el título */
        margin-bottom: 2rem; /* Más espacio hacia el contenido */
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
        padding: 0.75rem 1.5rem;
        background-color: #3b82f6;
        color: white;
        border-radius: 0.5rem;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s ease;
        width: fit-content;
        margin-left: auto;
        margin-right: auto;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        z-index: 10;
    }
}
