<?php
/**
 * English language file
 */
return [
    // General
    'site_title' => 'AI Prompt Generator',

    // Navigation
    'nav_home' => 'Home',
    'nav_examples' => 'Examples',
    'nav_how_to_use' => 'How to use',
    'nav_policies' => 'Policies',
    'nav_login' => 'Log in',
    'nav_signup' => 'Sign up',
    'nav_feedback' => 'Feedback',
    'nav_dark_mode' => 'Dark Mode',
    'nav_light_mode' => 'Light Mode',
    'profile' => 'Profile',
    'settings' => 'Settings',
    'logout' => 'Log out',

    // Headings
    'heading_main' => 'Which tool do you need a prompt for?',
    'heading_code_tools' => 'Code tools',
    'heading_writing_tools' => 'Writing tools',
    'main_heading' => 'Create perfect prompts',
    'sub_heading' => 'More professional',

    // Code tools
    'code_tool_title' => 'Coding',
    'code_tool_description' => 'For generating code, websites and applications',

    // Writing tools
    'writing_tool_title' => 'Writing',
    'writing_tool_description' => 'For generating texts, blogs and content',

    // Slide 1
    'which_ai_tool' => 'Which AI tool do you need a prompt for?',
    'policy_checkbox' => 'I have read and accept the <a href="/como-usar.php" class="text-blue-600 hover:text-blue-800 dark:text-blue-400">usage instructions</a> and <a href="/politicas.php" class="text-blue-600 hover:text-blue-800 dark:text-blue-400">privacy policies</a> of this tool.',

    // Slide 2
    'which_tool' => 'Which tool will you use?',
    'back' => 'Back',

    // Code form
    'code_form_title' => 'Describe your project',
    'code_form_project_type' => 'What type of project do you want to create?',
    'code_form_project_type_placeholder' => 'E.g.: Online store, Blog, Dashboard...',
    'code_form_features' => 'What main features do you need? What tech stack to use?',
    'code_form_features_placeholder' => 'E.g.: Shopping cart, user system...',
    'code_form_style' => 'What visual style do you prefer?',
    'code_form_style_placeholder' => 'E.g.: Minimalist, Modern, Corporate...',
    'code_form_submit' => 'Generate Prompt with AI',

    // Writing form
    'writing_form_title' => 'Describe your content',
    'writing_form_content_type' => 'What type of content do you want to create?',
    'writing_form_content_type_placeholder' => 'E.g.: Blog article, Email, Social media post...',
    'writing_form_main_topic' => 'What is the main topic?',
    'writing_form_main_topic_placeholder' => 'E.g.: Artificial intelligence, Digital marketing...',
    'writing_form_tone' => 'What tone of voice do you prefer?',
    'writing_form_tone_placeholder' => 'E.g.: Professional, Conversational, Inspiring...',
    'writing_form_submit' => 'Generate Prompt with AI',

    // Result
    'result_title' => 'Your Prompt is Ready',
    'result_copy' => 'Copy Prompt',
    'result_copied' => 'Copied!',
    'result_back' => 'Back',
    'result_generate_new' => 'Generate new prompt',

    // Modals
    'modal_error_title' => 'Error',
    'modal_error_message' => 'An error occurred while processing your request.',
    'modal_error_button' => 'Got it',
    'modal_rate_limit_title' => 'Rate limit exceeded',
    'modal_rate_limit_message' => 'You have reached the allowed request limit. Please try again later.',
    'modal_rate_limit_button' => 'Got it',

    // Footer
    'footer_copyright' => 'All rights reserved.',

    // Misc
    'loading' => 'Loading...',
    'language' => 'Language',
    'spanish' => 'Spanish',
    'english' => 'English',
    'loading_message' => 'Generating prompt with AI',
    'loading_wait' => 'Please wait a moment...',

    // Pricing
    'pricing_title' => 'Pricing',
    'pricing_hero_title' => 'Simple and Transparent Plans',
    'pricing_hero_subtitle' => 'Buy credits once and use them whenever you want. No subscriptions, no expiration.',
    'your_credits' => 'Your Credits',
    'credits' => 'credits',
    'per_credit' => 'per credit',
    'buy_credits' => 'Buy Credits',
    'most_popular' => 'Most Popular',
    'feature_ai_prompts' => 'Prompts for all AIs',
    'feature_no_expiry' => 'No expiration date',
    'feature_all_tools' => 'Access to all tools',
    'faq_title' => 'Frequently Asked Questions',
    'faq_what_are_credits' => 'What are credits?',
    'faq_credits_answer' => 'Credits are FastlyAI\'s virtual currency. Each time you generate a prompt, 1 credit is consumed.',
    'faq_credits_expire' => 'Do credits expire?',
    'faq_no_expiry_answer' => 'No, your credits never expire. You can use them whenever you want, no rush.',
    'faq_refund_policy' => 'What is the refund policy?',
    'faq_refund_answer' => 'We offer full refunds within the first 7 days if you are not satisfied with the service.',
    'view_profile' => 'View profile',

    // Policies Page
    'policies_title' => 'Privacy Policies',
    'data_collection_title' => 'Data Collection',
    'data_collection_text' => 'At FastlyAI we value your privacy. This tool does not store any of your personal data. The prompts you generate are not saved in our databases and are only used to provide the immediate service you request.',
    'ai_usage_title' => 'AI Usage',
    'ai_usage_text' => 'To improve the quality of interactions, we employ various artificial intelligence models provided by multiple providers. It is important to note that each of these providers (such as GPT, Claude, Gemini, among others) operates under their own data collection and privacy policies. We recommend reviewing the specific privacy policies of each provider to understand how information is managed.',
    'cookies_title' => 'Cookies and local storage',
    'cookies_text' => 'We use local storage (localStorage) only to remember your preferences, such as dark mode. We do not use cookies for tracking or to collect information about your behavior.',
    'policy_changes_title' => 'Changes to privacy policy',
    'policy_changes_text' => 'We reserve the right to modify this privacy policy at any time. Changes will take effect as soon as they are published on this page.',
    'terms_title' => 'Terms of Use',
    'responsible_use_title' => 'Responsible use',
    'responsible_use_text' => 'The user agrees to use this tool responsibly and ethically. We are not responsible for the misuse of generated prompts or content created from them.',
    'service_limitations_title' => 'Service limitations',
    'service_limitations_text' => 'FastlyAI is a helper tool for creating prompts, but we do not guarantee the effectiveness of results on all AI platforms. Results may vary depending on the final tool where the prompts are used.',
    'intellectual_property_title' => 'Intellectual property',
    'intellectual_property_text' => 'The prompts generated by this tool are free to use. We do not claim intellectual property over them or over content generated from their use on other platforms.',

    // How to Use Page
    'how_to_use_title' => 'How to Use PromptGen',
    'how_to_use_subtitle' => 'Learn how to use our tool to create effective prompts for different AI platforms.',
    'step_1_title' => 'Select the AI type',
    'step_1_text' => 'In the first step, choose whether you need a prompt to generate code or text. This choice will determine the options available in the following steps.',
    'step_1_coding' => 'Coding Type: For programming projects, web development, user interfaces, etc.',
    'step_1_writing' => 'Writing Type: For content creation, articles, posts and creative texts.',
    'step_2_title' => 'Choose the specific tool',
    'step_2_text' => 'Based on the selected type, we will show you different AI tools for which we can optimize the prompt.',
    'step_2_coding_tools' => 'For Coding:',
    'step_2_writing_tools' => 'For Writing:',
    'step_3_title' => 'Describe your project',
    'step_3_text' => 'Complete the form with specific information about your project. The more details you provide, the better the generated prompt will be.',
    'step_3_tips_title' => 'Tips for a good description:',
    'step_3_tip_1' => 'Be specific about what you want to create',
    'step_3_tip_2' => 'Mention key features or main topics',
    'step_3_tip_3' => 'Indicate the desired visual style or tone of voice',
    'step_3_tip_4' => 'For code, mention specific technologies',
    'step_3_tip_5' => 'For content, specify the target audience',
    'step_4_title' => 'Use the generated prompt',
    'step_4_text' => 'Once the prompt is generated, copy and paste it directly into the selected AI tool. The format is optimized to get the best results.',
    'step_4_quote' => 'Generated prompts are designed to be clear, structured and with the appropriate level of detail for each platform. You can modify them according to your specific needs.',
    'additional_tips_title' => 'Additional tips',
    'tip_specific_title' => 'Be specific',
    'tip_specific_text' => 'The more specific you are in your descriptions, the better results you will get. Avoid ambiguous terms and provide concrete examples of what you are looking for.',
    'tip_iterate_title' => 'Iterate when necessary',
    'tip_iterate_text' => 'You won\'t always get the perfect result on the first try. Use the initial prompt, evaluate the result and adjust as needed for subsequent iterations.',
    'tip_customize_title' => 'Customize prompts',
    'tip_customize_text' => 'Generated prompts are an excellent starting point, but feel free to customize them by adding additional information or modifying specific aspects.',
    'ready_to_start_title' => 'Ready to get started?',
    'ready_to_start_text' => 'Now that you know how PromptGen works, it\'s time to create prompts that enhance your projects.',
    'create_first_prompt' => 'Create my first prompt',

    // Examples Page
    'examples_title' => 'Examples',
    'examples_coding_section' => 'For Coding',
    'examples_writing_section' => 'For Writing',
    'example_dashboard_title' => 'Analytics dashboard',
    'example_task_app_title' => 'Task management application',
    'example_tech_article_title' => 'Article about technology trends',
    'example_email_campaign_title' => 'Email marketing campaign',
    'use_as_base' => 'Use as base',

    // Specific examples content
    'example_dashboard_prompt' => 'Tool: v0.dev

Create an analytics dashboard with the following features:
- Main panel with KPIs and key metrics
- Interactive performance charts
- Date and category filters
- Downloadable reports section
- Data table view

Visual style: Minimalist with blue color accents, clean and professional design.

Please generate the necessary code following current best practices and design patterns.',

    'example_task_app_prompt' => 'Tool: Lovable.dev

Create a task management application with the following features:
- Kanban board with drag and drop
- Color tag categorization
- Priority system
- Reminders and deadlines
- Calendar integration

Visual style: Modern and vibrant, with a color scheme that facilitates quick priority identification.

Please implement a modular architecture with reusable components and ensure the application is fully responsive.',

    'example_tech_article_prompt' => 'Tool: Jasper

Generate content for a blog article about technology trends
Main features:
- Focus on artificial intelligence and its impact on different industries
- Include statistics and real use cases
- Explain technical concepts in an accessible way
- Format with introduction, sections and clear conclusion
- Include frequently asked questions at the end

Tone of voice: Informative but conversational, aimed at an audience with basic technology knowledge.

Please generate the content maintaining a consistent and natural style.',

    'example_email_campaign_prompt' => 'Tool: Copy.ai

Generate content for a welcome email sequence
Main features:
- Series of 5 emails for new subscribers
- Progressive presentation of the brand and its values
- Include clear benefits for the reader
- Effective calls to action
- Attractive email subject for each message

Tone of voice: Friendly, enthusiastic and personal, as if you were talking directly to a friend.

Please ensure that each email has a clear purpose and that the complete sequence tells a cohesive story.',

    // Footer
    'footer_copyright' => '© 2025 FastlyAI - All rights reserved',

    // Typewriter effect
    'typewriter_phrases' => [
        'Faster.',
        'Easier.',
        'Simpler.',
        'More professional.',
        'No complications.'
    ],

    // Dynamic dark mode
    'dark_mode_text' => 'Dark Mode',
    'light_mode_text' => 'Light Mode',

    // Forms
    'form_describe_project' => 'Describe your project',
    'form_describe_content' => 'Describe your content',
    'form_project_type_label' => 'What type of project do you want to create?',
    'form_project_type_placeholder' => 'Ex: Online store, Blog, Dashboard...',
    'form_features_label' => 'What main features do you need? Technology stack to use?',
    'form_features_placeholder' => 'Ex: Shopping cart, user system...',
    'form_style_label' => 'What visual style do you prefer?',
    'form_style_placeholder' => 'Ex: Minimalist, Modern, Corporate...',
    'form_content_type_label' => 'What type of content do you want to create?',
    'form_content_type_placeholder' => 'Ex: Blog article, Email, Social media post...',
    'form_main_topic_label' => 'What is the main topic?',
    'form_main_topic_placeholder' => 'Ex: Artificial intelligence, Digital marketing...',
    'form_tone_label' => 'What tone of voice do you prefer?',
    'form_tone_placeholder' => 'Ex: Professional, Conversational, Inspiring...',
    'form_generate_prompt' => 'Generate Prompt with AI',
    'form_back' => 'Back',

    // History
    'history' => 'History',
    'no_history_yet' => 'No history yet',
    'view_prompt' => 'View prompt',
    'copy_prompt' => 'Copy prompt',
    'delete_prompt' => 'Delete',
    'history_empty' => 'Your history is empty',
    'history_empty_description' => 'Generate your first prompt to start seeing your history here.',
    'confirm_delete' => 'Are you sure you want to delete this prompt?',
    'delete_success' => 'Prompt deleted successfully',
    'copy_success' => 'Copied to clipboard',
];
