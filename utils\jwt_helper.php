<?php
require_once __DIR__ . '/../vendor/autoload.php';

use Firebase\JWT\JWT;
use Firebase\JWT\Key;
use Firebase\JWT\ExpiredException;
use Firebase\JWT\SignatureInvalidException;
use UnexpectedValueException; // Añadido para el manejo de token inválido

/**
 * Utilidad centralizada para manejo de JWT de Supabase
 * Proporciona funciones seguras para verificar y decodificar tokens JWT
 * Utiliza el enfoque JWKs (JSON Web Key Set) para compatibilidad con las nuevas claves de firma de Supabase.
 */
class JWTHelper {
    private static $supabaseUrl;
    private static $cachedJwks = null; // Para cachear las claves JWKs

    /**
     * Inicializar con la URL de Supabase para obtener las claves públicas (JWKs)
     */
    public static function init() {
        // Cargar variables de entorno si no están cargadas
        if (!isset($_ENV['SUPABASE_URL'])) {
            $dotenv = \Dotenv\Dotenv::createImmutable(__DIR__ . '/..');
            $dotenv->load();
        }
        
        self::$supabaseUrl = $_ENV['SUPABASE_URL'] ?? null;
        
        if (!self::$supabaseUrl) {
            throw new Exception('SUPABASE_URL no está configurado en las variables de entorno');
        }
    }

    /**
     * Obtener las claves públicas de Supabase del endpoint JWKs.
     * Cacha las claves para evitar múltiples llamadas HTTP.
     * @return array Las claves públicas en formato JWKs.
     * @throws Exception Si no se pueden obtener las claves.
     */
    private static function getSupabasePublicKeys() {
        self::init();

        // Si ya están cacheadas y no han expirado (puedes añadir lógica de tiempo)
        // Para simplificar, aquí no implementamos vencimiento explícito del caché,
        // pero en producción se debería refrescar periódicamente.
        if (self::$cachedJwks !== null) {
            return self::$cachedJwks;
        }

        $jwksUrl = self::$supabaseUrl . '/auth/v1/.well-known/jwks.json';
        
        $ch = curl_init($jwksUrl);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 5); // Timeout de 5 segundos
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);

        if ($httpCode !== 200 || $response === false) {
            error_log("Error al obtener JWKs de Supabase. HTTP Code: {$httpCode}, cURL Error: {$error}");
            throw new Exception("No se pudieron obtener las claves JWKs de Supabase.");
        }

        $jwks = json_decode($response, true);

        if (!isset($jwks['keys']) || !is_array($jwks['keys'])) {
            throw new Exception("Formato JWKs inválido recibido de Supabase.");
        }

        self::$cachedJwks = $jwks['keys'];
        return self::$cachedJwks;
    }
    
    /**
     * Verificar y decodificar un token JWT de Supabase
     * * @param string $token El token JWT a verificar
     * @return array|false Retorna los datos del usuario si es válido, false si no
     */
    public static function verifyAndDecodeToken($token) {
        try {
            self::init();
            
            if (empty($token)) {
                return false;
            }
            
            // Remover 'Bearer ' si está presente
            if (strpos($token, 'Bearer ') === 0) {
                $token = substr($token, 7);
            }
            
            // Obtener el header del token para el 'kid'
            $tks = explode('.', $token);
            if (count($tks) !== 3) {
                throw new UnexpectedValueException('Token JWT tiene un formato inválido.');
            }
            $head = json_decode(JWT::urlsafeB64Decode($tks[0]), true);

            if (!isset($head['kid'])) {
                throw new Exception('Token JWT no contiene un Key ID (kid) en el header.');
            }
            $targetKid = $head['kid'];

            // Obtener todas las claves públicas de Supabase
            $publicKeysData = self::getSupabasePublicKeys();
            
            // Buscar la clave específica que coincide con el 'kid' del token
            $signingKey = null;
            $algorithm = null;
            foreach ($publicKeysData as $keyData) {
                if (isset($keyData['kid']) && $keyData['kid'] === $targetKid) {
                    // Firebase\JWT\Key::createFromJWK es la forma correcta de usar los datos JWK
                    $signingKey = Key::createFromJWK($keyData);
                    $algorithm = $keyData['alg'];
                    break;
                }
            }

            if ($signingKey === null || $algorithm === null) {
                throw new Exception("No se encontró la clave de firma correspondiente al 'kid' del token.");
            }

            // Decodificar y verificar el token usando la clave específica y su algoritmo
            // Nota: Firebase\JWT::decode puede tomar un solo objeto Key o un array de ellos.
            // Si le pasas un solo objeto Key, debes especificar el algoritmo explícitamente.
            $decoded = JWT::decode($token, $signingKey, [$algorithm]);
            
            // Convertir a array para facilitar el uso
            $payload = (array) $decoded;
            
            // Verificar que el token tenga los campos necesarios
            if (!isset($payload['sub'])) { // 'email' puede no estar siempre presente en todos los tokens o roles
                error_log('Payload del JWT no contiene el campo "sub" (subject/user ID).');
                return false;
            }
            
            return [
                'user_id' => $payload['sub'],
                'email' => $payload['email'] ?? null, // Hacer el email opcional si no siempre está presente
                'role' => $payload['role'] ?? null,
                'exp' => $payload['exp'] ?? null,
                'iat' => $payload['iat'] ?? null,
                'full_payload' => $payload
            ];
            
        } catch (ExpiredException $e) {
            error_log('JWT Token expirado: ' . $e->getMessage());
            return false;
        } catch (SignatureInvalidException $e) {
            error_log('JWT Firma inválida: ' . $e->getMessage());
            return false;
        } catch (UnexpectedValueException $e) { // Para errores de formato de token
            error_log('JWT Token con formato inválido: ' . $e->getMessage());
            return false;
        } catch (Exception $e) {
            error_log('Error general al verificar JWT: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Obtener el token JWT desde los headers de la solicitud
     * * @return string|null El token JWT o null si no se encuentra
     */
    public static function getTokenFromHeaders() {
        // En Apache, getallheaders() suele funcionar. En Nginx/PHP-FPM, $_SERVER['HTTP_AUTHORIZATION'] es más fiable.
        $headers = [];
        if (function_exists('getallheaders')) {
            $headers = getallheaders();
        } else {
            // Fallback para entornos donde getallheaders() no está disponible
            foreach ($_SERVER as $name => $value) {
                if (substr($name, 0, 5) == 'HTTP_') {
                    $headers[str_replace(' ', '-', ucwords(strtolower(str_replace('_', ' ', substr($name, 5)))))] = $value;
                }
            }
        }
        
        // Buscar en el header Authorization
        if (isset($headers['Authorization'])) {
            return $headers['Authorization'];
        }
        
        // Buscar en el header authorization (minúsculas, algunos clientes pueden enviarlo así)
        if (isset($headers['authorization'])) {
            return $headers['authorization'];
        }
        
        return null;
    }
    
    /**
     * Verificar si un usuario está autenticado basado en el token JWT
     * * @return array|false Retorna los datos del usuario si está autenticado, false si no
     */
    public static function getCurrentUser() {
        $token = self::getTokenFromHeaders();
        
        if (!$token) {
            return false;
        }
        
        return self::verifyAndDecodeToken($token);
    }
    
    /**
     * Middleware para proteger endpoints que requieren autenticación
     * * @param bool $returnJson Si debe retornar JSON en caso de error (default: true)
     * @return array|void Retorna los datos del usuario o termina la ejecución si no está autenticado
     */
    public static function requireAuth($returnJson = true) {
        $user = self::getCurrentUser();
        
        if (!$user) {
            if ($returnJson) {
                http_response_code(401);
                header('Content-Type: application/json');
                echo json_encode([
                    'error' => 'Token de autenticación requerido o inválido.',
                    'status' => 401
                ]);
            } else {
                http_response_code(401);
                echo 'No autorizado';
            }
            exit;
        }
        
        return $user;
    }
}